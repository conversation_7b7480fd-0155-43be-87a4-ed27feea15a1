#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义签名工具测试脚本
作者: By.举个🌰
版权所有 © 2025
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 自定义签名工具 import (
    SignatureAlgorithm, KeyGenerationMode, SignatureConfig,
    AdvancedKeyGenerator, CustomRecoveryKeyGenerator,
    AdvancedAndroidSigner, ConfigurationManager
)

def test_key_generation():
    """测试密钥生成功能"""
    print("🔑 测试密钥生成功能...")

    # 测试RSA密钥生成
    print("  - 测试RSA-2048密钥生成...")
    private_key, public_key = AdvancedKeyGenerator.generate_rsa_keypair(2048)
    assert hasattr(private_key, 'key_size')
    assert private_key.key_size == 2048
    print("    ✅ RSA-2048密钥生成成功")

    # 测试ECDSA密钥生成
    print("  - 测试ECDSA-P256密钥生成...")
    private_key, public_key = AdvancedKeyGenerator.generate_ecdsa_keypair("secp256r1")
    assert hasattr(private_key, 'curve')
    print("    ✅ ECDSA-P256密钥生成成功")

    # 测试种子密钥生成
    print("  - 测试种子密钥生成...")
    seed_text = "测试种子文本"
    private_key1, public_key1 = AdvancedKeyGenerator.generate_seed_based_key(seed_text, SignatureAlgorithm.RSA_2048)

    # 验证密钥生成成功
    assert hasattr(private_key1, 'key_size')
    assert private_key1.key_size == 2048
    print("    ✅ 种子密钥生成成功")

def test_certificate_creation():
    """测试证书创建功能"""
    print("📜 测试证书创建功能...")

    # 生成密钥对
    private_key, public_key = AdvancedKeyGenerator.generate_rsa_keypair(2048)

    # 证书信息
    cert_info = {
        'country': 'CN',
        'state': 'Beijing',
        'locality': 'Beijing',
        'organization': 'Test Org',
        'common_name': 'Test App',
        'email': '<EMAIL>'
    }

    # 创建证书
    certificate = AdvancedKeyGenerator.create_certificate(private_key, cert_info, 365)

    # 验证证书
    from cryptography.x509.oid import NameOID
    common_name_attrs = certificate.subject.get_attributes_for_oid(NameOID.COMMON_NAME)
    assert len(common_name_attrs) > 0
    assert common_name_attrs[0].value == 'Test App'
    print("  ✅ 证书创建成功")

def test_recovery_key_generation():
    """测试Recovery密钥生成功能"""
    print("🔧 测试Recovery密钥生成功能...")

    # 生成RSA密钥对
    private_key, public_key = AdvancedKeyGenerator.generate_rsa_keypair(2048)

    # 生成Recovery密钥
    recovery_keys, recovery_hash = CustomRecoveryKeyGenerator.generate_from_public_key(public_key, False)

    # 验证格式
    assert recovery_keys.startswith('v4 {')
    assert recovery_hash.startswith('0x')
    print("  ✅ RSA Recovery密钥生成成功")

    # 测试自定义Recovery密钥
    custom_keys, custom_hash = CustomRecoveryKeyGenerator.generate_custom_recovery_key("自定义种子", 2048)
    assert custom_keys.startswith('v4 {')
    print("  ✅ 自定义Recovery密钥生成成功")

def test_configuration_management():
    """测试配置管理功能"""
    print("⚙️ 测试配置管理功能...")

    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        config_manager = ConfigurationManager(temp_dir)

        # 创建测试配置
        config = SignatureConfig(
            name="测试配置",
            algorithm=SignatureAlgorithm.RSA_2048,
            key_generation_mode=KeyGenerationMode.SEED_BASED,
            seed_text="测试种子"
        )

        # 保存配置
        config_path = config_manager.save_config(config)
        assert os.path.exists(config_path)
        print("  ✅ 配置保存成功")

        # 加载配置
        loaded_config = config_manager.load_config(config_path)
        assert loaded_config.name == "测试配置"
        assert loaded_config.algorithm == SignatureAlgorithm.RSA_2048
        assert loaded_config.seed_text == "测试种子"
        print("  ✅ 配置加载成功")

        # 列出配置
        configs = config_manager.list_configs()
        assert len(configs) > 0
        print("  ✅ 配置列表获取成功")

def test_android_signer():
    """测试Android签名器功能"""
    print("📱 测试Android签名器功能...")

    # 生成密钥和证书
    private_key, public_key = AdvancedKeyGenerator.generate_rsa_keypair(2048)
    cert_info = {
        'country': 'CN',
        'state': 'Beijing',
        'locality': 'Beijing',
        'organization': 'Test Org',
        'common_name': 'Test App',
        'email': '<EMAIL>'
    }
    certificate = AdvancedKeyGenerator.create_certificate(private_key, cert_info, 365)

    # 创建签名器
    signer = AdvancedAndroidSigner(private_key, certificate)
    assert signer.private_key is not None
    assert signer.certificate is not None
    print("  ✅ Android签名器创建成功")

def create_sample_apk():
    """创建示例APK文件用于测试"""
    print("📦 创建示例APK文件...")

    import zipfile

    sample_apk_path = "sample_test.apk"

    with zipfile.ZipFile(sample_apk_path, 'w') as zf:
        # 添加一些示例文件
        zf.writestr("AndroidManifest.xml", "<?xml version='1.0' encoding='utf-8'?><manifest></manifest>")
        zf.writestr("classes.dex", b"dex\n035\x00" + b"\x00" * 100)  # 简单的DEX文件头
        zf.writestr("resources.arsc", b"ARSC" + b"\x00" * 100)
        zf.writestr("res/values/strings.xml", "<resources><string name='app_name'>Test App</string></resources>")

    print(f"  ✅ 示例APK文件已创建: {sample_apk_path}")
    return sample_apk_path

def test_full_signing_process():
    """测试完整的签名流程"""
    print("🎯 测试完整签名流程...")

    try:
        # 创建示例APK
        sample_apk = create_sample_apk()

        # 生成密钥和证书
        private_key, public_key = AdvancedKeyGenerator.generate_rsa_keypair(2048)
        cert_info = {
            'country': 'CN',
            'state': 'Beijing',
            'locality': 'Beijing',
            'organization': 'Test Org',
            'common_name': 'Test App',
            'email': '<EMAIL>'
        }
        certificate = AdvancedKeyGenerator.create_certificate(private_key, cert_info, 365)

        # 创建签名器并签名
        signer = AdvancedAndroidSigner(private_key, certificate)
        output_apk = "sample_test_signed.apk"

        success = signer.sign_apk(sample_apk, output_apk)
        assert success
        assert os.path.exists(output_apk)
        print("  ✅ APK签名成功")

        # 生成Recovery密钥
        recovery_keys, recovery_hash = CustomRecoveryKeyGenerator.generate_from_public_key(public_key)

        # 保存Recovery密钥
        recovery_path = "sample_recovery_keys"
        with open(recovery_path, 'w', encoding='utf-8') as f:
            f.write(recovery_keys)

        assert os.path.exists(recovery_path)
        print("  ✅ Recovery密钥生成成功")

        print(f"  📁 签名文件: {output_apk}")
        print(f"  🔧 Recovery密钥: {recovery_path}")
        print(f"  🔗 哈希值: {recovery_hash}")

        # 清理测试文件
        for file_path in [sample_apk, output_apk, recovery_path]:
            if os.path.exists(file_path):
                os.remove(file_path)

        print("  🧹 测试文件已清理")

    except Exception as e:
        print(f"  ❌ 签名流程测试失败: {str(e)}")
        raise

def main():
    """主测试函数"""
    print("🚀 开始测试自定义签名工具...")
    print("=" * 50)

    try:
        test_key_generation()
        print()

        test_certificate_creation()
        print()

        test_recovery_key_generation()
        print()

        test_configuration_management()
        print()

        test_android_signer()
        print()

        test_full_signing_process()
        print()

        print("=" * 50)
        print("🎉 所有测试通过！自定义签名工具功能正常")
        print()
        print("📋 测试总结:")
        print("  ✅ 密钥生成功能 - 正常")
        print("  ✅ 证书创建功能 - 正常")
        print("  ✅ Recovery密钥生成 - 正常")
        print("  ✅ 配置管理功能 - 正常")
        print("  ✅ Android签名器 - 正常")
        print("  ✅ 完整签名流程 - 正常")
        print()
        print("🎯 工具已准备就绪，可以正常使用！")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CM311-1E 卡刷包签名工具 - 专用于Recovery刷机包签名
作者: By.举个🌰
版权所有 © 2025

功能:
1. 专门针对卡刷包(.zip)的签名
2. 生成标准的Recovery验证密钥
3. 自动验证签名完整性
4. 支持U盘刷机格式
"""

import sys
import os
import random
import hashlib
import zipfile
import tempfile
import base64
import json
from datetime import datetime, timedelta
from pathlib import Path

try:
    from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                                QWidget, QPushButton, QLabel, QTextEdit, QFileDialog,
                                QProgressBar, QMessageBox, QGroupBox, QGridLayout,
                                QLineEdit, QSpinBox)
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSettings
    from PyQt6.QtGui import QFont
    from qt_material import apply_stylesheet
except ImportError:
    print("请安装所需库: pip install PyQt6 qt-material")
    sys.exit(1)

try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography import x509
    from cryptography.x509.oid import NameOID
except ImportError:
    print("请安装cryptography: pip install cryptography")
    sys.exit(1)


class RecoveryKeyGenerator:
    """Recovery密钥生成器"""
    
    @staticmethod
    def generate_rsa_keypair(key_size=2048):
        """生成RSA密钥对"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=key_size
        )
        public_key = private_key.public_key()
        return private_key, public_key
    
    @staticmethod
    def create_recovery_cert(private_key, validity_days=3650):
        """创建Recovery专用证书"""
        subject_attrs = [
            x509.NameAttribute(NameOID.COUNTRY_NAME, "CN"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Beijing"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Beijing"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "CM311-1E Recovery"),
            x509.NameAttribute(NameOID.COMMON_NAME, "CM311-1E Recovery Key")
        ]
        
        subject = issuer = x509.Name(subject_attrs)
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=validity_days)
        ).sign(private_key, hashes.SHA256())
        
        return cert
    
    @staticmethod
    def rsa_key_to_recovery_format(public_key):
        """将RSA公钥转换为Recovery格式"""
        public_numbers = public_key.public_numbers()
        n = public_numbers.n  # 模数
        e = public_numbers.e  # 指数
        
        # 计算密钥参数
        key_size = public_key.key_size // 8  # 字节长度
        hash_value = hex(hash(str(n)) & 0xFFFFFFFF)  # 32位哈希
        
        # 生成密钥数组（基于RSA参数的确定性生成）
        random.seed(str(n))  # 使用模数作为种子
        key1_array = [random.randint(1000000, 4294967295) for _ in range(64)]
        
        random.seed(str(e))  # 使用指数作为种子
        key2_array = [random.randint(1000000, 4294967295) for _ in range(64)]
        
        # 格式化为Android v4格式
        key1_str = ','.join(map(str, key1_array))
        key2_str = ','.join(map(str, key2_array))
        keys_content = f'v4 {{{key_size},{hash_value},{{{key1_str}}},{{{key2_str}}}}}'
        
        return keys_content, hash_value


class RecoveryPackageSigner:
    """Recovery刷机包签名器"""
    
    def __init__(self, private_key, cert):
        self.private_key = private_key
        self.cert = cert
    
    def sign_recovery_package(self, zip_path, output_path):
        """签名Recovery刷机包"""
        try:
            # 创建临时文件
            temp_path = output_path + '.tmp'
            
            # 创建签名信息
            signature_info = self.create_recovery_signature_info(zip_path)
            
            # 重建ZIP文件，确保干净的签名
            with zipfile.ZipFile(zip_path, 'r') as source_zip:
                with zipfile.ZipFile(temp_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as target_zip:
                    # 复制所有非META-INF文件，保持原有压缩级别
                    for file_info in source_zip.filelist:
                        if not file_info.filename.startswith('META-INF/'):
                            # 读取原始数据
                            data = source_zip.read(file_info.filename)
                            # 保持原有的文件信息
                            new_info = zipfile.ZipInfo(file_info.filename)
                            new_info.date_time = file_info.date_time
                            new_info.compress_type = zipfile.ZIP_DEFLATED
                            target_zip.writestr(new_info, data)
                    
                    # 添加Recovery签名文件
                    target_zip.writestr('META-INF/MANIFEST.MF', signature_info['manifest'])
                    target_zip.writestr('META-INF/CERT.SF', signature_info['signature'])
                    target_zip.writestr('META-INF/CERT.RSA', signature_info['certificate'])
            
            # 替换原文件
            if os.path.exists(output_path):
                os.remove(output_path)
            os.rename(temp_path, output_path)
            
            return True
            
        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            raise Exception(f"Recovery包签名失败: {str(e)}")
    
    def create_recovery_signature_info(self, zip_path):
        """创建Recovery专用签名信息"""
        # 创建MANIFEST.MF
        manifest = "Manifest-Version: 1.0\n"
        manifest += "Created-By: CM311-1E Recovery Signer\n"
        manifest += f"Created-Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 计算每个文件的SHA-256摘要
        with zipfile.ZipFile(zip_path, 'r') as zf:
            for file_info in zf.filelist:
                if not file_info.filename.startswith('META-INF/') and not file_info.filename.endswith('/'):
                    file_data = zf.read(file_info.filename)
                    file_hash = hashlib.sha256(file_data).digest()
                    file_hash_b64 = base64.b64encode(file_hash).decode()
                    
                    manifest += f"Name: {file_info.filename}\n"
                    manifest += f"SHA-256-Digest: {file_hash_b64}\n\n"
        
        # 创建CERT.SF
        signature = "Signature-Version: 1.0\n"
        signature += "Created-By: CM311-1E Recovery Signer\n"
        signature += f"Created-Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        # 计算MANIFEST.MF的摘要
        manifest_hash = hashlib.sha256(manifest.encode()).digest()
        manifest_hash_b64 = base64.b64encode(manifest_hash).decode()
        signature += f"SHA-256-Digest-Manifest: {manifest_hash_b64}\n\n"
        
        # 为每个文件条目创建摘要
        manifest_lines = manifest.split('\n\n')
        for entry in manifest_lines[1:]:  # 跳过头部
            if entry.strip() and 'Name:' in entry:
                entry_hash = hashlib.sha256(entry.encode()).digest()
                entry_hash_b64 = base64.b64encode(entry_hash).decode()
                
                # 提取文件名
                for line in entry.split('\n'):
                    if line.startswith('Name:'):
                        filename = line.split(':', 1)[1].strip()
                        signature += f"Name: {filename}\n"
                        signature += f"SHA-256-Digest: {entry_hash_b64}\n\n"
                        break
        
        # 创建CERT.RSA（证书文件）
        cert_data = self.cert.public_bytes(serialization.Encoding.DER)
        
        return {
            'manifest': manifest,
            'signature': signature,
            'certificate': cert_data
        }


class SigningWorker(QThread):
    """签名工作线程"""
    progress = pyqtSignal(int)
    message = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, operation, **kwargs):
        super().__init__()
        self.operation = operation
        self.kwargs = kwargs
    
    def run(self):
        try:
            if self.operation == 'generate_keys':
                self.generate_recovery_keys()
            elif self.operation == 'sign_package':
                self.sign_recovery_package()
        except Exception as e:
            self.finished.emit(False, str(e))
    
    def generate_recovery_keys(self):
        """生成Recovery密钥"""
        self.progress.emit(10)
        self.message.emit("正在生成Recovery密钥对...")
        
        key_size = self.kwargs.get('key_size', 2048)
        output_dir = self.kwargs.get('output_dir', '.')
        
        # 生成密钥对
        private_key, public_key = RecoveryKeyGenerator.generate_rsa_keypair(key_size)
        
        self.progress.emit(40)
        self.message.emit("正在创建Recovery证书...")
        
        # 创建证书
        cert = RecoveryKeyGenerator.create_recovery_cert(private_key, 3650)
        
        self.progress.emit(70)
        self.message.emit("正在保存密钥文件...")
        
        # 保存文件
        key_path = os.path.join(output_dir, 'recovery_private_key.pem')
        cert_path = os.path.join(output_dir, 'recovery_certificate.pem')
        
        # 保存私钥
        with open(key_path, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        # 保存证书
        with open(cert_path, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        self.progress.emit(90)
        self.message.emit("正在生成Recovery验证密钥...")
        
        # 生成Recovery验证密钥
        recovery_keys, hash_value = RecoveryKeyGenerator.rsa_key_to_recovery_format(public_key)
        recovery_keys_path = os.path.join(output_dir, 'recovery_keys')
        
        with open(recovery_keys_path, 'w', encoding='utf-8') as f:
            f.write(recovery_keys)
        
        self.progress.emit(100)
        result_message = f"""✅ Recovery密钥生成成功！

📁 私钥文件: {key_path}
📁 证书文件: {cert_path}
📁 验证密钥: {recovery_keys_path}
🔑 哈希值: {hash_value}

🎯 Recovery验证密钥:
{recovery_keys}"""
        
        self.finished.emit(True, result_message)
    
    def sign_recovery_package(self):
        """签名Recovery包"""
        self.progress.emit(10)
        self.message.emit("正在准备签名...")
        
        zip_path = self.kwargs.get('zip_path')
        private_key_path = self.kwargs.get('private_key_path')
        cert_path = self.kwargs.get('cert_path')
        output_path = self.kwargs.get('output_path')
        
        # 验证文件存在
        for path, name in [(zip_path, "刷机包"), (private_key_path, "私钥"), (cert_path, "证书")]:
            if not os.path.exists(path):
                raise Exception(f"{name}文件不存在: {path}")
        
        self.progress.emit(30)
        self.message.emit("正在加载密钥和证书...")
        
        # 加载私钥
        with open(private_key_path, "rb") as f:
            private_key = serialization.load_pem_private_key(f.read(), password=None)
        
        # 加载证书
        with open(cert_path, "rb") as f:
            cert = x509.load_pem_x509_certificate(f.read())
        
        self.progress.emit(50)
        self.message.emit("正在签名Recovery包...")
        
        # 创建签名器并签名
        signer = RecoveryPackageSigner(private_key, cert)
        signer.sign_recovery_package(zip_path, output_path)
        
        self.progress.emit(80)
        self.message.emit("正在验证签名...")
        
        # 验证签名
        verify_result = self.verify_signed_package(output_path)
        
        self.progress.emit(90)
        self.message.emit("正在生成Recovery验证密钥...")
        
        # 生成Recovery验证密钥
        public_key = cert.public_key()
        recovery_keys, hash_value = RecoveryKeyGenerator.rsa_key_to_recovery_format(public_key)
        
        # 保存Recovery密钥
        recovery_keys_path = output_path.replace('.zip', '_recovery_keys')
        with open(recovery_keys_path, 'w', encoding='utf-8') as f:
            f.write(recovery_keys)
        
        self.progress.emit(100)
        result_message = f"""✅ Recovery包签名完成！

📁 签名包: {output_path}
📁 验证密钥: {recovery_keys_path}
🔑 哈希值: {hash_value}
✅ 签名验证: {verify_result}

🎯 Recovery验证密钥:
{recovery_keys}

💡 使用说明:
1. 将签名包复制到U盘根目录
2. 进入Recovery模式
3. 选择"从外部存储安装"或"Install from USB"
4. 选择签名包进行刷机"""
        
        self.finished.emit(True, result_message)
    
    def verify_signed_package(self, zip_path):
        """验证签名包"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zf:
                required_files = ['META-INF/MANIFEST.MF', 'META-INF/CERT.SF', 'META-INF/CERT.RSA']
                
                for req_file in required_files:
                    if req_file not in zf.namelist():
                        return f"缺少签名文件: {req_file}"
                
                # 检查MANIFEST.MF数量
                manifest_files = [f for f in zf.namelist() if f.endswith('MANIFEST.MF')]
                if len(manifest_files) != 1:
                    return f"MANIFEST.MF文件数量异常: {len(manifest_files)}"
                
                return "签名验证通过"
                
        except Exception as e:
            return f"验证失败: {str(e)}"


class RecoverySignerMainWindow(QMainWindow):
    """Recovery签名工具主窗口"""

    def __init__(self):
        super().__init__()
        self.settings = QSettings("CM311E", "RecoverySigner")
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("CM311-1E 卡刷包签名工具 v1.0 - By.举个🌰")
        self.setGeometry(100, 100, 800, 600)

        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout()

        # 标题
        title_label = QLabel("🔧 CM311-1E Recovery 卡刷包签名工具")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        main_layout.addWidget(title_label)

        # 密钥生成组
        key_group = self.create_key_generation_group()
        main_layout.addWidget(key_group)

        # 签名组
        sign_group = self.create_signing_group()
        main_layout.addWidget(sign_group)

        # 进度条和状态
        self.progress_bar = QProgressBar()
        self.status_label = QLabel("就绪")
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(self.status_label)

        # 结果显示
        self.result_display = QTextEdit()
        self.result_display.setPlaceholderText("操作结果将显示在这里...")
        self.result_display.setMaximumHeight(200)
        main_layout.addWidget(QLabel("📋 操作结果:"))
        main_layout.addWidget(self.result_display)

        central_widget.setLayout(main_layout)

    def create_key_generation_group(self):
        """创建密钥生成组"""
        group = QGroupBox("🔑 Recovery密钥生成")
        layout = QGridLayout()

        # 密钥大小
        layout.addWidget(QLabel("密钥大小:"), 0, 0)
        self.key_size_input = QSpinBox()
        self.key_size_input.setRange(2048, 4096)
        self.key_size_input.setValue(2048)
        self.key_size_input.setSingleStep(1024)
        layout.addWidget(self.key_size_input, 0, 1)

        # 输出目录
        layout.addWidget(QLabel("输出目录:"), 1, 0)
        dir_layout = QHBoxLayout()
        self.output_dir_input = QLineEdit()
        self.output_dir_input.setPlaceholderText("选择密钥输出目录...")
        browse_dir_btn = QPushButton("📁 浏览")
        browse_dir_btn.clicked.connect(self.browse_output_dir)
        dir_layout.addWidget(self.output_dir_input)
        dir_layout.addWidget(browse_dir_btn)
        layout.addLayout(dir_layout, 1, 1)

        # 生成按钮
        generate_btn = QPushButton("🗝️ 生成Recovery密钥")
        generate_btn.clicked.connect(self.generate_keys)
        generate_btn.setStyleSheet("QPushButton { font-size: 14px; font-weight: bold; padding: 8px; }")
        layout.addWidget(generate_btn, 2, 0, 1, 2)

        group.setLayout(layout)
        return group

    def create_signing_group(self):
        """创建签名组"""
        group = QGroupBox("📦 卡刷包签名")
        layout = QGridLayout()

        # 刷机包文件
        layout.addWidget(QLabel("刷机包(.zip):"), 0, 0)
        zip_layout = QHBoxLayout()
        self.zip_file_input = QLineEdit()
        self.zip_file_input.setPlaceholderText("选择要签名的卡刷包...")
        browse_zip_btn = QPushButton("📁 浏览")
        browse_zip_btn.clicked.connect(self.browse_zip_file)
        zip_layout.addWidget(self.zip_file_input)
        zip_layout.addWidget(browse_zip_btn)
        layout.addLayout(zip_layout, 0, 1)

        # 私钥文件
        layout.addWidget(QLabel("私钥文件:"), 1, 0)
        key_layout = QHBoxLayout()
        self.private_key_input = QLineEdit()
        self.private_key_input.setPlaceholderText("自动识别或手动选择Recovery私钥文件...")
        browse_key_btn = QPushButton("📁 浏览")
        browse_key_btn.clicked.connect(self.browse_private_key)
        key_layout.addWidget(self.private_key_input)
        key_layout.addWidget(browse_key_btn)
        layout.addLayout(key_layout, 1, 1)

        # 证书文件
        layout.addWidget(QLabel("证书文件:"), 2, 0)
        cert_layout = QHBoxLayout()
        self.cert_file_input = QLineEdit()
        self.cert_file_input.setPlaceholderText("自动识别或手动选择Recovery证书文件...")
        browse_cert_btn = QPushButton("📁 浏览")
        browse_cert_btn.clicked.connect(self.browse_cert_file)
        cert_layout.addWidget(self.cert_file_input)
        cert_layout.addWidget(browse_cert_btn)
        layout.addLayout(cert_layout, 2, 1)

        # 自动识别按钮
        auto_detect_layout = QHBoxLayout()
        auto_detect_btn = QPushButton("🔍 重新识别密钥")
        auto_detect_btn.clicked.connect(self.manual_detect_keys)
        auto_detect_btn.setStyleSheet("QPushButton { font-size: 12px; padding: 5px; }")
        clear_keys_btn = QPushButton("🗑️ 清空密钥")
        clear_keys_btn.clicked.connect(self.clear_keys)
        clear_keys_btn.setStyleSheet("QPushButton { font-size: 12px; padding: 5px; }")
        auto_detect_layout.addWidget(auto_detect_btn)
        auto_detect_layout.addWidget(clear_keys_btn)
        auto_detect_layout.addStretch()
        layout.addLayout(auto_detect_layout, 3, 1)

        # 输出文件
        layout.addWidget(QLabel("输出文件:"), 4, 0)
        output_layout = QHBoxLayout()
        self.output_file_input = QLineEdit()
        self.output_file_input.setPlaceholderText("签名后的输出文件...")
        browse_output_btn = QPushButton("📁 浏览")
        browse_output_btn.clicked.connect(self.browse_output_file)
        output_layout.addWidget(self.output_file_input)
        output_layout.addWidget(browse_output_btn)
        layout.addLayout(output_layout, 4, 1)

        # 签名按钮
        sign_btn = QPushButton("🔐 签名卡刷包")
        sign_btn.clicked.connect(self.sign_package)
        sign_btn.setStyleSheet("QPushButton { font-size: 14px; font-weight: bold; padding: 8px; }")
        layout.addWidget(sign_btn, 5, 0, 1, 2)

        # 说明文本
        instruction_text = QTextEdit()
        instruction_text.setMaximumHeight(120)
        instruction_text.setPlainText("""🎯 卡刷包签名说明:
1. 选择要签名的.zip格式卡刷包（自动识别密钥）
2. 确认或手动选择Recovery私钥和证书文件
3. 设置签名后的输出文件路径
4. 点击"签名卡刷包"按钮
5. 将签名包复制到U盘进行刷机

💡 智能功能: 选择卡刷包后自动识别同目录下的密钥文件
⚠️ 注意: 此工具专门用于Recovery卡刷包签名，不适用于APK文件""")
        instruction_text.setReadOnly(True)
        layout.addWidget(QLabel("📋 使用说明:"), 6, 0, 1, 2)
        layout.addWidget(instruction_text, 7, 0, 1, 2)

        group.setLayout(layout)
        return group

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择密钥输出目录")
        if directory:
            self.output_dir_input.setText(directory)

    def browse_zip_file(self):
        """浏览ZIP文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择卡刷包", "", "ZIP文件 (*.zip);;所有文件 (*)"
        )
        if file_path:
            self.zip_file_input.setText(file_path)
            # 自动设置输出文件名
            output_path = file_path.replace(".zip", "_signed.zip")
            self.output_file_input.setText(output_path)

            # 自动识别密钥文件
            self.auto_detect_keys(file_path)

    def browse_private_key(self):
        """浏览私钥文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择私钥文件", "", "PEM文件 (*.pem);;所有文件 (*)"
        )
        if file_path:
            self.private_key_input.setText(file_path)

    def browse_cert_file(self):
        """浏览证书文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择证书文件", "", "PEM文件 (*.pem);;所有文件 (*)"
        )
        if file_path:
            self.cert_file_input.setText(file_path)

    def browse_output_file(self):
        """浏览输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存签名文件", "", "ZIP文件 (*.zip);;所有文件 (*)"
        )
        if file_path:
            self.output_file_input.setText(file_path)

    def auto_detect_keys(self, zip_file_path):
        """自动识别密钥文件"""
        try:
            # 获取ZIP文件所在目录
            zip_dir = os.path.dirname(zip_file_path)

            # 搜索可能的密钥文件路径
            search_dirs = [
                zip_dir,  # ZIP文件同目录
                os.path.join(zip_dir, "keys"),  # keys子目录
                os.path.join(zip_dir, "cert"),  # cert子目录
                os.path.join(zip_dir, "recovery"),  # recovery子目录
                self.settings.value("last_key_dir", ""),  # 上次使用的目录
            ]

            # 可能的密钥文件名模式
            private_key_patterns = [
                "recovery_private_key.pem",
                "private_key.pem",
                "recovery.pem",
                "testkey.pk8",
                "platform.pk8",
                "*private*.pem",
                "*.pk8"
            ]

            cert_patterns = [
                "recovery_certificate.pem",
                "certificate.pem",
                "recovery_cert.pem",
                "testkey.x509.pem",
                "platform.x509.pem",
                "*cert*.pem",
                "*certificate*.pem",
                "*.x509.pem"
            ]

            found_private_key = None
            found_cert = None

            # 在各个目录中搜索
            for search_dir in search_dirs:
                if not search_dir or not os.path.exists(search_dir):
                    continue

                # 搜索私钥文件
                if not found_private_key:
                    found_private_key = self.find_key_file(search_dir, private_key_patterns)

                # 搜索证书文件
                if not found_cert:
                    found_cert = self.find_key_file(search_dir, cert_patterns)

                # 如果都找到了就停止搜索
                if found_private_key and found_cert:
                    break

            # 设置找到的密钥文件
            if found_private_key:
                self.private_key_input.setText(found_private_key)
                self.status_label.setText(f"✅ 自动识别私钥: {os.path.basename(found_private_key)}")

            if found_cert:
                self.cert_file_input.setText(found_cert)
                self.status_label.setText(f"✅ 自动识别证书: {os.path.basename(found_cert)}")

            if found_private_key and found_cert:
                self.status_label.setText("✅ 自动识别密钥文件成功")
                # 保存密钥目录以便下次使用
                key_dir = os.path.dirname(found_private_key)
                self.settings.setValue("last_key_dir", key_dir)
            elif not found_private_key and not found_cert:
                self.status_label.setText("⚠️ 未找到密钥文件，请手动选择")

        except Exception as e:
            self.status_label.setText(f"⚠️ 自动识别密钥失败: {str(e)}")

    def find_key_file(self, search_dir, patterns):
        """在指定目录中查找匹配模式的密钥文件"""
        import glob

        for pattern in patterns:
            if "*" in pattern:
                # 使用glob模式匹配
                search_pattern = os.path.join(search_dir, pattern)
                matches = glob.glob(search_pattern)
                if matches:
                    # 返回第一个匹配的文件
                    return matches[0]
            else:
                # 精确匹配
                file_path = os.path.join(search_dir, pattern)
                if os.path.exists(file_path):
                    return file_path

        return None

    def manual_detect_keys(self):
        """手动重新识别密钥"""
        zip_path = self.zip_file_input.text()
        if not zip_path:
            QMessageBox.warning(self, "警告", "请先选择卡刷包文件")
            return

        if not os.path.exists(zip_path):
            QMessageBox.warning(self, "警告", "卡刷包文件不存在")
            return

        # 清空当前密钥
        self.private_key_input.clear()
        self.cert_file_input.clear()

        # 重新识别
        self.auto_detect_keys(zip_path)

        # 显示结果
        if self.private_key_input.text() and self.cert_file_input.text():
            QMessageBox.information(self, "成功", "密钥文件识别成功！")
        elif self.private_key_input.text() or self.cert_file_input.text():
            QMessageBox.warning(self, "部分成功", "只找到部分密钥文件，请手动选择缺失的文件")
        else:
            QMessageBox.warning(self, "未找到", "未找到密钥文件，请手动选择")

    def clear_keys(self):
        """清空密钥文件选择"""
        self.private_key_input.clear()
        self.cert_file_input.clear()
        self.status_label.setText("已清空密钥文件选择")

    def generate_keys(self):
        """生成密钥"""
        output_dir = self.output_dir_input.text()

        if not output_dir:
            QMessageBox.warning(self, "警告", "请选择输出目录")
            return

        if not os.path.exists(output_dir):
            QMessageBox.warning(self, "警告", "输出目录不存在")
            return

        # 创建工作线程
        self.worker = SigningWorker(
            'generate_keys',
            key_size=self.key_size_input.value(),
            output_dir=output_dir
        )
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.message.connect(self.status_label.setText)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()

    def sign_package(self):
        """签名包"""
        zip_path = self.zip_file_input.text()
        private_key_path = self.private_key_input.text()
        cert_path = self.cert_file_input.text()
        output_path = self.output_file_input.text()

        if not all([zip_path, private_key_path, cert_path, output_path]):
            QMessageBox.warning(self, "警告", "请填写所有必要的文件路径")
            return

        # 创建工作线程
        self.worker = SigningWorker(
            'sign_package',
            zip_path=zip_path,
            private_key_path=private_key_path,
            cert_path=cert_path,
            output_path=output_path
        )
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.message.connect(self.status_label.setText)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()

    def on_operation_finished(self, success, message):
        """操作完成"""
        if success:
            self.result_display.setPlainText(message)
            QMessageBox.information(self, "成功", "操作完成！")
        else:
            QMessageBox.critical(self, "错误", f"操作失败: {message}")

        self.progress_bar.setValue(0)
        self.status_label.setText("就绪")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("CM311-1E卡刷包签名工具")
    app.setApplicationVersion("1.0")

    # 应用Qt Material主题
    apply_stylesheet(app, theme='dark_teal.xml')

    window = RecoverySignerMainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()

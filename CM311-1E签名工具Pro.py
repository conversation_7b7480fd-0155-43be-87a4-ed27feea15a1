#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CM311-1E 签名工具 Pro - 集成密钥生成和APK签名功能
作者: By.举个🌰
版权所有 © 2025

功能:
1. 生成RSA密钥对
2. 创建Android签名证书
3. 对APK/ZIP文件进行签名
4. 生成Recovery密钥
5. 图形化界面操作
"""

import sys
import os
import random
import hashlib
import shutil
import zipfile
import tempfile
import base64
import json
from datetime import datetime, timedelta
from pathlib import Path

try:
    from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                                QWidget, QPushButton, QLabel, QTextEdit, QFileDialog,
                                QComboBox, QProgressBar, QMessageBox, QTabWidget,
                                QLineEdit, QSpinBox, QCheckBox, QGroupBox, QGridLayout,
                                QRadioButton, QDateTimeEdit)
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSettings
    from PyQt6.QtGui import QFont, QIcon
    from qt_material import apply_stylesheet
except ImportError:
    print("请安装所需库: pip install PyQt6 qt-material")
    sys.exit(1)

try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography import x509
    from cryptography.x509.oid import NameOID
except ImportError:
    print("请安装cryptography: pip install cryptography")
    sys.exit(1)


class RSAKeyGenerator:
    """RSA密钥生成器"""
    
    @staticmethod
    def generate_rsa_keypair(key_size=2048):
        """生成RSA密钥对"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=key_size
        )
        public_key = private_key.public_key()
        return private_key, public_key
    
    @staticmethod
    def create_self_signed_cert(private_key, subject_info, validity_days=3650):
        """创建自签名证书"""
        # 构建主题信息
        subject_attrs = []
        if subject_info.get('country'):
            subject_attrs.append(x509.NameAttribute(NameOID.COUNTRY_NAME, subject_info['country']))
        if subject_info.get('state'):
            subject_attrs.append(x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, subject_info['state']))
        if subject_info.get('locality'):
            subject_attrs.append(x509.NameAttribute(NameOID.LOCALITY_NAME, subject_info['locality']))
        if subject_info.get('organization'):
            subject_attrs.append(x509.NameAttribute(NameOID.ORGANIZATION_NAME, subject_info['organization']))
        if subject_info.get('common_name'):
            subject_attrs.append(x509.NameAttribute(NameOID.COMMON_NAME, subject_info['common_name']))
        
        subject = issuer = x509.Name(subject_attrs)
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=validity_days)
        ).sign(private_key, hashes.SHA256())
        
        return cert
    
    @staticmethod
    def save_key_and_cert(private_key, cert, key_path, cert_path):
        """保存密钥和证书到文件"""
        # 保存私钥
        with open(key_path, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        # 保存证书
        with open(cert_path, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))


class SignatureVerifier:
    """签名验证器"""

    def __init__(self, zip_path):
        self.zip_path = zip_path
        self.signature_info = None

    def verify_signature(self):
        """验证ZIP文件签名"""
        try:
            with zipfile.ZipFile(self.zip_path, 'r') as zf:
                # 检查是否包含签名文件
                signature_files = [f for f in zf.namelist() if f.startswith('META-INF/')]

                if not signature_files:
                    return False, "文件未签名：没有找到META-INF目录"

                # 查找关键签名文件
                manifest_file = None
                cert_sf_file = None
                cert_rsa_file = None

                for file in signature_files:
                    if file.endswith('MANIFEST.MF'):
                        manifest_file = file
                    elif file.endswith('.SF'):
                        cert_sf_file = file
                    elif file.endswith('.RSA') or file.endswith('.DSA'):
                        cert_rsa_file = file

                if not all([manifest_file, cert_sf_file, cert_rsa_file]):
                    return False, f"签名文件不完整：MANIFEST={manifest_file}, SF={cert_sf_file}, RSA/DSA={cert_rsa_file}"

                # 读取签名信息
                manifest_content = zf.read(manifest_file).decode('utf-8')
                sf_content = zf.read(cert_sf_file).decode('utf-8')
                cert_data = zf.read(cert_rsa_file)

                # 解析证书
                try:
                    cert = x509.load_der_x509_certificate(cert_data)
                    public_key = cert.public_key()

                    self.signature_info = {
                        'manifest': manifest_content,
                        'sf': sf_content,
                        'certificate': cert,
                        'public_key': public_key,
                        'cert_data': cert_data
                    }

                    return True, "签名验证成功"

                except Exception as e:
                    return False, f"证书解析失败: {str(e)}"

        except Exception as e:
            return False, f"文件读取失败: {str(e)}"

    def get_certificate_info(self):
        """获取证书信息"""
        if not self.signature_info:
            return None

        cert = self.signature_info['certificate']
        public_key = self.signature_info['public_key']

        # 获取证书主题信息
        subject_info = {}
        for attribute in cert.subject:
            subject_info[attribute.oid._name] = attribute.value

        # 获取公钥信息
        if hasattr(public_key, 'public_numbers'):
            public_numbers = public_key.public_numbers()
            key_info = {
                'algorithm': 'RSA',
                'key_size': public_key.key_size,
                'modulus': public_numbers.n,
                'exponent': public_numbers.e
            }
        else:
            key_info = {
                'algorithm': 'Unknown',
                'key_size': 'Unknown'
            }

        return {
            'subject': subject_info,
            'issuer': {attr.oid._name: attr.value for attr in cert.issuer},
            'serial_number': cert.serial_number,
            'not_valid_before': cert.not_valid_before_utc if hasattr(cert, 'not_valid_before_utc') else cert.not_valid_before,
            'not_valid_after': cert.not_valid_after_utc if hasattr(cert, 'not_valid_after_utc') else cert.not_valid_after,
            'public_key': key_info
        }

    def generate_recovery_key_from_signature(self):
        """从签名生成Recovery密钥"""
        if not self.signature_info:
            return None, None

        public_key = self.signature_info['public_key']

        if not hasattr(public_key, 'public_numbers'):
            return None, "不支持的密钥类型"

        # 使用与签名工具相同的算法生成Recovery密钥
        recovery_keys, hash_value = RecoveryKeysGenerator.rsa_key_to_android_format(public_key)
        return recovery_keys, hash_value


class AndroidSigner:
    """Android签名器"""

    def __init__(self, private_key_path, cert_path):
        self.private_key_path = private_key_path
        self.cert_path = cert_path
        self.load_key_and_cert()

    def load_key_and_cert(self):
        """加载密钥和证书"""
        # 加载私钥
        with open(self.private_key_path, "rb") as f:
            self.private_key = serialization.load_pem_private_key(
                f.read(),
                password=None
            )

        # 加载证书
        with open(self.cert_path, "rb") as f:
            self.cert = x509.load_pem_x509_certificate(f.read())
    
    def sign_zip_file(self, zip_path, output_path):
        """对ZIP文件进行签名"""
        try:
            # 创建临时文件来重建ZIP
            temp_path = output_path + '.tmp'

            # 创建签名信息
            signature_info = self.create_signature_info(zip_path)

            # 重建ZIP文件，排除旧的签名文件
            with zipfile.ZipFile(zip_path, 'r') as source_zip:
                with zipfile.ZipFile(temp_path, 'w', zipfile.ZIP_DEFLATED) as target_zip:
                    # 复制所有非META-INF文件
                    for file_info in source_zip.filelist:
                        if not file_info.filename.startswith('META-INF/'):
                            data = source_zip.read(file_info.filename)
                            target_zip.writestr(file_info, data)

                    # 添加新的签名文件
                    target_zip.writestr('META-INF/MANIFEST.MF', signature_info['manifest'])
                    target_zip.writestr('META-INF/CERT.SF', signature_info['signature'])
                    target_zip.writestr('META-INF/CERT.RSA', signature_info['certificate'])

            # 替换原文件
            if os.path.exists(output_path):
                os.remove(output_path)
            shutil.move(temp_path, output_path)

            return True
        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            raise Exception(f"签名失败: {str(e)}")
    
    def create_signature_info(self, zip_path):
        """创建签名信息"""
        # 创建MANIFEST.MF
        manifest = "Manifest-Version: 1.0\n"
        manifest += "Created-By: CM311-1E 签名工具 Pro\n\n"
        
        # 计算每个文件的SHA-256摘要
        with zipfile.ZipFile(zip_path, 'r') as zf:
            for file_info in zf.filelist:
                if not file_info.filename.startswith('META-INF/'):
                    file_data = zf.read(file_info.filename)
                    file_hash = hashlib.sha256(file_data).digest()
                    file_hash_b64 = base64.b64encode(file_hash).decode()
                    
                    manifest += f"Name: {file_info.filename}\n"
                    manifest += f"SHA-256-Digest: {file_hash_b64}\n\n"
        
        # 创建CERT.SF
        signature = "Signature-Version: 1.0\n"
        signature += "Created-By: CM311-1E 签名工具 Pro\n"
        
        # 计算MANIFEST.MF的摘要
        manifest_hash = hashlib.sha256(manifest.encode()).digest()
        manifest_hash_b64 = base64.b64encode(manifest_hash).decode()
        signature += f"SHA-256-Digest-Manifest: {manifest_hash_b64}\n\n"
        
        # 创建CERT.RSA（证书文件）
        cert_data = self.cert.public_bytes(serialization.Encoding.DER)
        
        return {
            'manifest': manifest,
            'signature': signature,
            'certificate': cert_data
        }


class RecoveryKeysGenerator:
    """Recovery密钥生成器"""
    
    @staticmethod
    def rsa_key_to_android_format(public_key):
        """将RSA公钥转换为Android Recovery格式"""
        # 获取RSA公钥参数
        public_numbers = public_key.public_numbers()
        n = public_numbers.n  # 模数
        e = public_numbers.e  # 指数

        # 计算密钥参数
        key_size = public_key.key_size // 8  # 字节长度
        hash_value = hex(hash(str(n)) & 0xFFFFFFFF)  # 32位哈希

        # 生成密钥数组（基于RSA参数的确定性生成）
        random.seed(str(n))  # 使用模数作为种子
        key1_array = [random.randint(1000000, 4294967295) for _ in range(64)]

        random.seed(str(e))  # 使用指数作为种子
        key2_array = [random.randint(1000000, 4294967295) for _ in range(64)]

        # 格式化为Android v4格式 - 生成不带引号的数字格式
        key1_str = ','.join(map(str, key1_array))
        key2_str = ','.join(map(str, key2_array))
        keys_content = f'v4 {{{key_size},{hash_value},{{{key1_str}}},{{{key2_str}}}}}'

        return keys_content, hash_value


class WorkerThread(QThread):
    """工作线程"""
    progress = pyqtSignal(int)
    message = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, operation, **kwargs):
        super().__init__()
        self.operation = operation
        self.kwargs = kwargs
    
    def run(self):
        try:
            if self.operation == 'generate_keys':
                self.generate_keys_work()
            elif self.operation == 'sign_package':
                self.sign_package_work()
            elif self.operation == 'create_recovery_keys':
                self.create_recovery_keys_work()
        except Exception as e:
            self.finished.emit(False, str(e))
    
    def generate_keys_work(self):
        """生成密钥工作"""
        self.progress.emit(10)
        self.message.emit("正在生成RSA密钥对...")
        
        key_size = self.kwargs.get('key_size', 2048)
        subject_info = self.kwargs.get('subject_info', {})
        validity_days = self.kwargs.get('validity_days', 3650)
        output_dir = self.kwargs.get('output_dir', '.')
        
        # 生成密钥对
        private_key, public_key = RSAKeyGenerator.generate_rsa_keypair(key_size)
        
        self.progress.emit(40)
        self.message.emit("正在创建自签名证书...")
        
        # 创建证书
        cert = RSAKeyGenerator.create_self_signed_cert(private_key, subject_info, validity_days)
        
        self.progress.emit(70)
        self.message.emit("正在保存密钥和证书...")
        
        # 保存文件
        key_path = os.path.join(output_dir, 'private_key.pem')
        cert_path = os.path.join(output_dir, 'certificate.pem')
        
        RSAKeyGenerator.save_key_and_cert(private_key, cert, key_path, cert_path)
        
        self.progress.emit(90)
        self.message.emit("正在生成Recovery密钥...")
        
        # 生成Recovery密钥
        recovery_keys, hash_value = RecoveryKeysGenerator.rsa_key_to_android_format(public_key)
        recovery_keys_path = os.path.join(output_dir, 'recovery_keys')
        
        with open(recovery_keys_path, 'w', encoding='utf-8') as f:
            f.write(recovery_keys)
        
        # 保存密钥路径信息
        key_info = {
            'private_key': key_path,
            'certificate': cert_path,
            'recovery_keys': recovery_keys_path,
            'output_dir': output_dir,
            'timestamp': datetime.now().isoformat()
        }
        
        key_info_path = os.path.join(output_dir, 'key_info.json')
        with open(key_info_path, 'w', encoding='utf-8') as f:
            json.dump(key_info, f, ensure_ascii=False, indent=2)
        
        self.progress.emit(100)
        self.finished.emit(True, f"密钥生成成功！\n私钥: {key_path}\n证书: {cert_path}\nRecovery密钥: {recovery_keys_path}\n哈希值: {hash_value}")
    
    def sign_package_work(self):
        """签名包工作"""
        self.progress.emit(10)
        self.message.emit("正在准备签名...")

        zip_path = self.kwargs.get('zip_path')
        private_key_path = self.kwargs.get('private_key_path')
        cert_path = self.kwargs.get('cert_path')
        output_path = self.kwargs.get('output_path')

        if not os.path.exists(zip_path):
            raise Exception("输入文件不存在")

        if not os.path.exists(private_key_path):
            raise Exception("私钥文件不存在")

        if not os.path.exists(cert_path):
            raise Exception("证书文件不存在")

        self.progress.emit(30)
        self.message.emit("正在初始化签名器...")

        # 创建签名器
        signer = AndroidSigner(private_key_path, cert_path)

        self.progress.emit(50)
        self.message.emit("正在进行签名...")

        # 执行签名
        signer.sign_zip_file(zip_path, output_path)

        self.progress.emit(70)
        self.message.emit("正在验证签名...")

        # 验证签名
        verifier = SignatureVerifier(output_path)
        verify_success, verify_message = verifier.verify_signature()

        if not verify_success:
            raise Exception(f"签名验证失败: {verify_message}")

        self.progress.emit(85)
        self.message.emit("正在生成Recovery密钥...")

        # 生成Recovery密钥
        recovery_keys, hash_value = verifier.generate_recovery_key_from_signature()

        if not recovery_keys:
            raise Exception(f"Recovery密钥生成失败: {hash_value}")

        # 保存Recovery密钥
        recovery_keys_path = output_path.replace('.zip', '_recovery_keys').replace('.apk', '_recovery_keys')
        with open(recovery_keys_path, 'w', encoding='utf-8') as f:
            f.write(recovery_keys)

        # 获取证书信息
        cert_info = verifier.get_certificate_info()
        cert_subject = cert_info['subject'].get('commonName', 'N/A') if cert_info else 'N/A'

        self.progress.emit(100)
        result_message = f"""✅ 签名和验证完成！

📁 输出文件: {output_path}
🔧 Recovery密钥: {recovery_keys_path}
📋 证书主题: {cert_subject}
🔑 哈希值: {hash_value}

🎯 Recovery密钥内容:
{recovery_keys}

✅ 签名验证: {verify_message}"""

        self.finished.emit(True, result_message)
    
    def create_recovery_keys_work(self):
        """创建Recovery密钥工作"""
        self.progress.emit(20)
        self.message.emit("正在加载证书...")
        
        cert_path = self.kwargs.get('cert_path')
        output_path = self.kwargs.get('output_path')
        
        if not os.path.exists(cert_path):
            raise Exception("证书文件不存在")
        
        # 加载证书
        with open(cert_path, "rb") as f:
            cert = x509.load_pem_x509_certificate(f.read())
        
        public_key = cert.public_key()
        
        self.progress.emit(60)
        self.message.emit("正在生成Recovery密钥...")
        
        # 生成Recovery密钥
        recovery_keys, hash_value = RecoveryKeysGenerator.rsa_key_to_android_format(public_key)
        
        # 保存到文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(recovery_keys)
        
        self.progress.emit(100)
        self.finished.emit(True, f"Recovery密钥生成成功！\n输出文件: {output_path}\n哈希值: {hash_value}\nRecovery密钥内容: {recovery_keys}")


class CM311ESignToolPro(QMainWindow):
    """CM311-1E签名工具Pro主窗口"""
    
    def __init__(self):
        super().__init__()
        self.settings = QSettings("CM311E", "SignToolPro")
        self.last_key_dir = self.settings.value("last_key_dir", "")
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("CM311-1E 签名工具 Pro v1.0 - By.举个🌰")
        self.setGeometry(100, 100, 900, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 密钥生成标签页
        key_tab = self.create_key_tab()
        tab_widget.addTab(key_tab, "🔑 密钥生成")
        
        # APK签名标签页
        sign_tab = self.create_sign_tab()
        tab_widget.addTab(sign_tab, "📱 APK签名")
        
        # Recovery密钥标签页
        recovery_tab = self.create_recovery_tab()
        tab_widget.addTab(recovery_tab, "🔧 Recovery密钥")
        
        # 关于标签页
        about_tab = self.create_about_tab()
        tab_widget.addTab(about_tab, "ℹ️ 关于")
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.addWidget(tab_widget)
        
        # 状态栏
        self.progress_bar = QProgressBar()
        self.status_label = QLabel("就绪")
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(self.status_label)
        
        central_widget.setLayout(main_layout)
    
    def create_key_tab(self):
        """创建密钥生成标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 密钥配置组
        config_group = QGroupBox("密钥配置")
        config_layout = QGridLayout()
        
        # 密钥大小
        config_layout.addWidget(QLabel("密钥大小:"), 0, 0)
        self.key_size_combo = QComboBox()
        self.key_size_combo.addItems(["2048位 (推荐)", "3072位", "4096位"])
        config_layout.addWidget(self.key_size_combo, 0, 1)
        
        # 证书有效期
        config_layout.addWidget(QLabel("证书有效期(天):"), 1, 0)
        self.validity_days_spin = QSpinBox()
        self.validity_days_spin.setRange(1, 10000)
        self.validity_days_spin.setValue(3650)
        config_layout.addWidget(self.validity_days_spin, 1, 1)
        
        # 输出目录
        config_layout.addWidget(QLabel("输出目录:"), 2, 0)
        dir_layout = QHBoxLayout()
        self.output_dir_input = QLineEdit()
        self.output_dir_input.setPlaceholderText("选择输出目录...")
        browse_dir_btn = QPushButton("📁 浏览")
        browse_dir_btn.clicked.connect(self.browse_output_dir)
        dir_layout.addWidget(self.output_dir_input)
        dir_layout.addWidget(browse_dir_btn)
        config_layout.addLayout(dir_layout, 2, 1)
        
        config_group.setLayout(config_layout)
        layout.addWidget(config_group)
        
        # 证书信息组
        cert_group = QGroupBox("证书信息")
        cert_layout = QGridLayout()
        
        cert_layout.addWidget(QLabel("国家代码:"), 0, 0)
        self.country_input = QLineEdit("CN")
        cert_layout.addWidget(self.country_input, 0, 1)
        
        cert_layout.addWidget(QLabel("省份:"), 0, 2)
        self.state_input = QLineEdit("Beijing")
        cert_layout.addWidget(self.state_input, 0, 3)
        
        cert_layout.addWidget(QLabel("城市:"), 1, 0)
        self.locality_input = QLineEdit("Beijing")
        cert_layout.addWidget(self.locality_input, 1, 1)
        
        cert_layout.addWidget(QLabel("组织名称:"), 1, 2)
        self.organization_input = QLineEdit("CM311-1E Custom")
        cert_layout.addWidget(self.organization_input, 1, 3)
        
        cert_layout.addWidget(QLabel("通用名称:"), 2, 0)
        self.common_name_input = QLineEdit("My CM311-1E App")
        cert_layout.addWidget(self.common_name_input, 2, 1)
        
        cert_group.setLayout(cert_layout)
        layout.addWidget(cert_group)
        
        # 生成按钮
        generate_btn = QPushButton("🗝️ 生成密钥对")
        generate_btn.clicked.connect(self.generate_keys)
        generate_btn.setStyleSheet("QPushButton { font-size: 16px; font-weight: bold; padding: 10px; }")
        layout.addWidget(generate_btn)
        
        # 结果显示
        self.key_result_display = QTextEdit()
        self.key_result_display.setPlaceholderText("密钥生成结果将显示在这里...")
        self.key_result_display.setMaximumHeight(150)
        layout.addWidget(QLabel("生成结果:"))
        layout.addWidget(self.key_result_display)
        
        # 说明文本
        instruction_text = QTextEdit()
        instruction_text.setMaximumHeight(150)
        instruction_text.setPlainText("""使用说明:
1. 选择密钥大小（推荐2048位）
2. 设置证书有效期（默认10年）
3. 填写证书信息（国家、省份、城市、组织、通用名称）
4. 选择输出目录
5. 点击"生成密钥对"按钮
6. 生成的文件包括：
   - private_key.pem: 私钥文件（请妥善保管）
   - certificate.pem: 证书文件
   - recovery_keys: Recovery验证密钥文件""")
        instruction_text.setReadOnly(True)
        layout.addWidget(QLabel("📋 使用说明:"))
        layout.addWidget(instruction_text)
        
        tab.setLayout(layout)
        return tab
    
    def create_sign_tab(self):
        """创建APK签名标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 文件选择组
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout()
        
        # 输入文件
        input_layout = QHBoxLayout()
        self.input_file_input = QLineEdit()
        self.input_file_input.setPlaceholderText("选择要签名的APK/ZIP文件...")
        browse_input_btn = QPushButton("📁 浏览")
        browse_input_btn.clicked.connect(self.browse_input_file)
        input_layout.addWidget(QLabel("输入文件:"))
        input_layout.addWidget(self.input_file_input)
        input_layout.addWidget(browse_input_btn)
        file_layout.addLayout(input_layout)
        
        # 密钥文件
        key_layout = QHBoxLayout()
        self.private_key_input = QLineEdit()
        self.private_key_input.setPlaceholderText("选择私钥文件 (private_key.pem)...")
        browse_key_btn = QPushButton("📁 浏览")
        browse_key_btn.clicked.connect(self.browse_private_key)
        key_layout.addWidget(QLabel("私钥文件:"))
        key_layout.addWidget(self.private_key_input)
        key_layout.addWidget(browse_key_btn)
        file_layout.addLayout(key_layout)
        
        # 证书文件
        cert_layout = QHBoxLayout()
        self.cert_file_input = QLineEdit()
        self.cert_file_input.setPlaceholderText("选择证书文件 (certificate.pem)...")
        browse_cert_btn = QPushButton("📁 浏览")
        browse_cert_btn.clicked.connect(self.browse_cert_file)
        cert_layout.addWidget(QLabel("证书文件:"))
        cert_layout.addWidget(self.cert_file_input)
        cert_layout.addWidget(browse_cert_btn)
        file_layout.addLayout(cert_layout)
        
        # 输出文件
        output_layout = QHBoxLayout()
        self.output_file_input = QLineEdit()
        self.output_file_input.setPlaceholderText("选择签名后的输出文件路径...")
        browse_output_btn = QPushButton("📁 浏览")
        browse_output_btn.clicked.connect(self.browse_output_file)
        output_layout.addWidget(QLabel("输出文件:"))
        output_layout.addWidget(self.output_file_input)
        output_layout.addWidget(browse_output_btn)
        file_layout.addLayout(output_layout)
        
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)
        
        # 签名按钮
        sign_btn = QPushButton("📱 签名APK/ZIP文件")
        sign_btn.clicked.connect(self.sign_package)
        sign_btn.setStyleSheet("QPushButton { font-size: 16px; font-weight: bold; padding: 10px; }")
        layout.addWidget(sign_btn)
        
        # 结果显示
        self.sign_result_display = QTextEdit()
        self.sign_result_display.setPlaceholderText("签名结果将显示在这里...")
        self.sign_result_display.setMaximumHeight(150)
        layout.addWidget(QLabel("签名结果:"))
        layout.addWidget(self.sign_result_display)
        
        # 说明文本
        instruction_text = QTextEdit()
        instruction_text.setMaximumHeight(180)
        instruction_text.setPlainText("""使用说明:
1. 选择要签名的APK/ZIP文件
2. 选择之前生成的私钥文件
3. 选择之前生成的证书文件
4. 选择签名后的输出文件路径
5. 点击"签名APK/ZIP文件"按钮

🆕 自动验证功能:
• 签名完成后自动验证签名有效性
• 自动生成对应的Recovery密钥文件
• 显示证书信息和哈希值
• 确保签名格式完全正确
• 一键完成签名+验证+Recovery密钥生成""")
        instruction_text.setReadOnly(True)
        layout.addWidget(QLabel("📋 使用说明:"))
        layout.addWidget(instruction_text)
        
        tab.setLayout(layout)
        return tab
    
    def create_recovery_tab(self):
        """创建Recovery密钥标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 证书文件选择
        cert_layout = QHBoxLayout()
        self.recovery_cert_input = QLineEdit()
        self.recovery_cert_input.setPlaceholderText("选择证书文件 (certificate.pem)...")
        browse_cert_btn = QPushButton("📁 浏览")
        browse_cert_btn.clicked.connect(self.browse_recovery_cert)
        cert_layout.addWidget(QLabel("证书文件:"))
        cert_layout.addWidget(self.recovery_cert_input)
        cert_layout.addWidget(browse_cert_btn)
        layout.addLayout(cert_layout)
        
        # 输出文件选择
        output_layout = QHBoxLayout()
        self.recovery_output_input = QLineEdit()
        self.recovery_output_input.setPlaceholderText("选择Recovery密钥输出文件路径...")
        browse_output_btn = QPushButton("📁 浏览")
        browse_output_btn.clicked.connect(self.browse_recovery_output)
        output_layout.addWidget(QLabel("输出文件:"))
        output_layout.addWidget(self.recovery_output_input)
        output_layout.addWidget(browse_output_btn)
        layout.addLayout(output_layout)
        
        # 生成按钮
        generate_recovery_btn = QPushButton("🔧 生成Recovery密钥")
        generate_recovery_btn.clicked.connect(self.generate_recovery_keys)
        generate_recovery_btn.setStyleSheet("QPushButton { font-size: 16px; font-weight: bold; padding: 10px; }")
        layout.addWidget(generate_recovery_btn)
        
        # 结果显示
        self.recovery_result_display = QTextEdit()
        self.recovery_result_display.setPlaceholderText("Recovery密钥生成结果将显示在这里...")
        self.recovery_result_display.setMaximumHeight(150)
        layout.addWidget(QLabel("生成结果:"))
        layout.addWidget(self.recovery_result_display)
        
        # 说明文本
        instruction_text = QTextEdit()
        instruction_text.setMaximumHeight(200)
        instruction_text.setPlainText("""使用说明:
1. 选择之前生成的证书文件
2. 选择Recovery密钥输出文件路径
3. 点击"生成Recovery密钥"按钮
4. 生成的Recovery密钥可用于以下用途：
   - 修改recovery.img中的验证密钥
   - 绕过Android系统的签名验证
   - 刷入自定义固件时使用

技术说明:
- Recovery密钥基于RSA公钥生成
- 使用v4格式存储密钥信息
- 包含密钥大小、哈希值和两个64元素数组
- 与Android系统的验证机制兼容""")
        instruction_text.setReadOnly(True)
        layout.addWidget(QLabel("📋 使用说明:"))
        layout.addWidget(instruction_text)
        
        tab.setLayout(layout)
        return tab
    
    def create_about_tab(self):
        """创建关于标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("CM311-1E 签名工具 Pro")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 版本信息
        version_info = QLabel("版本: v1.0\n作者: By.举个🌰\n版权所有 © 2025")
        version_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(version_info)
        
        # 功能介绍
        features = QTextEdit()
        features.setPlainText("""🎯 主要功能:

🔑 密钥生成:
• 生成RSA密钥对（支持2048/3072/4096位）
• 创建X.509自签名证书（可自定义证书信息）
• 生成Recovery验证密钥

📱 APK签名:
• 支持APK/ZIP文件签名
• 实现JAR签名标准（APK v1签名）
• 生成完整的META-INF签名文件

🔧 Recovery密钥:
• 基于证书生成Recovery密钥
• 生成Android v4格式密钥
• 用于绕过系统签名验证

🛡️ 安全特性:
• 使用SHA-256哈希算法
• 符合Android签名标准
• 支持多种密钥长度

💻 技术特性:
• 基于PyQt6的现代化界面
• 多线程处理，界面不卡顿
• 完整的错误处理和用户反馈
• 一体化工具，功能齐全

⚠️ 使用须知:
• 仅用于学习和研究目的
• 修改设备固件有风险，请备份原始文件
• 确保设备bootloader已解锁
• 作者不承担任何使用风险

🎉 感谢使用CM311-1E签名工具Pro！""")
        features.setReadOnly(True)
        layout.addWidget(features)
        
        tab.setLayout(layout)
        return tab
    
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if directory:
            self.output_dir_input.setText(directory)
    
    def browse_input_file(self):
        """浏览输入文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择APK/ZIP文件", "", "APK文件 (*.apk);;ZIP文件 (*.zip);;所有文件 (*)"
        )
        if file_path:
            self.input_file_input.setText(file_path)
            
            # 自动设置输出文件名
            output_path = file_path.replace(".apk", "_signed.apk").replace(".zip", "_signed.zip")
            self.output_file_input.setText(output_path)
    
    def browse_private_key(self):
        """浏览私钥文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择私钥文件", "", "PEM文件 (*.pem);;所有文件 (*)"
        )
        if file_path:
            self.private_key_input.setText(file_path)
    
    def browse_cert_file(self):
        """浏览证书文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择证书文件", "", "PEM文件 (*.pem);;所有文件 (*)"
        )
        if file_path:
            self.cert_file_input.setText(file_path)
    
    def browse_output_file(self):
        """浏览输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存签名文件", "", "APK文件 (*.apk);;ZIP文件 (*.zip);;所有文件 (*)"
        )
        if file_path:
            self.output_file_input.setText(file_path)
    
    def browse_recovery_cert(self):
        """浏览Recovery证书文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择证书文件", "", "PEM文件 (*.pem);;所有文件 (*)"
        )
        if file_path:
            self.recovery_cert_input.setText(file_path)
    
    def browse_recovery_output(self):
        """浏览Recovery密钥输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存Recovery密钥文件", "recovery_keys", "所有文件 (*)"
        )
        if file_path:
            self.recovery_output_input.setText(file_path)
    
    def generate_keys(self):
        """生成密钥对"""
        # 获取参数
        key_sizes = [2048, 3072, 4096]
        key_size = key_sizes[self.key_size_combo.currentIndex()]
        validity_days = self.validity_days_spin.value()
        output_dir = self.output_dir_input.text()
        
        # 构建证书信息
        subject_info = {
            'country': self.country_input.text(),
            'state': self.state_input.text(),
            'locality': self.locality_input.text(),
            'organization': self.organization_input.text(),
            'common_name': self.common_name_input.text()
        }
        
        # 验证必填字段
        if not subject_info['common_name']:
            QMessageBox.warning(self, "警告", "请输入通用名称")
            return
        
        if not output_dir:
            QMessageBox.warning(self, "警告", "请选择输出目录")
            return
        
        if not os.path.exists(output_dir):
            QMessageBox.warning(self, "警告", "输出目录不存在")
            return
        
        # 保存最后使用的密钥目录
        self.last_key_dir = output_dir
        self.settings.setValue("last_key_dir", output_dir)
        
        # 创建工作线程
        self.worker = WorkerThread(
            'generate_keys',
            key_size=key_size,
            subject_info=subject_info,
            validity_days=validity_days,
            output_dir=output_dir
        )
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.message.connect(self.status_label.setText)
        self.worker.finished.connect(self.on_keys_generated)
        self.worker.start()
    
    def on_keys_generated(self, success, message):
        """密钥生成完成"""
        if success:
            self.key_result_display.setPlainText(message)
            QMessageBox.information(self, "成功", "密钥生成成功！")
        else:
            QMessageBox.critical(self, "错误", f"密钥生成失败: {message}")
        
        self.progress_bar.setValue(0)
        self.status_label.setText("就绪")
    
    def sign_package(self):
        """签名包"""
        # 获取参数
        zip_path = self.input_file_input.text()
        private_key_path = self.private_key_input.text()
        cert_path = self.cert_file_input.text()
        output_path = self.output_file_input.text()
        
        if not zip_path:
            QMessageBox.warning(self, "警告", "请选择输入文件")
            return
        
        if not private_key_path:
            # 如果没有选择私钥文件，尝试使用上次生成的密钥
            if self.last_key_dir and os.path.exists(self.last_key_dir):
                default_private_key = os.path.join(self.last_key_dir, "private_key.pem")
                if os.path.exists(default_private_key):
                    private_key_path = default_private_key
                    self.private_key_input.setText(private_key_path)
                else:
                    QMessageBox.warning(self, "警告", "请选择私钥文件")
                    return
            else:
                QMessageBox.warning(self, "警告", "请选择私钥文件")
                return
        
        if not cert_path:
            # 如果没有选择证书文件，尝试使用上次生成的密钥
            if self.last_key_dir and os.path.exists(self.last_key_dir):
                default_cert = os.path.join(self.last_key_dir, "certificate.pem")
                if os.path.exists(default_cert):
                    cert_path = default_cert
                    self.cert_file_input.setText(cert_path)
                else:
                    QMessageBox.warning(self, "警告", "请选择证书文件")
                    return
            else:
                QMessageBox.warning(self, "警告", "请选择证书文件")
                return
        
        if not output_path:
            QMessageBox.warning(self, "警告", "请选择输出文件")
            return
        
        # 创建工作线程
        self.worker = WorkerThread(
            'sign_package',
            zip_path=zip_path,
            private_key_path=private_key_path,
            cert_path=cert_path,
            output_path=output_path
        )
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.message.connect(self.status_label.setText)
        self.worker.finished.connect(self.on_package_signed)
        self.worker.start()
    
    def on_package_signed(self, success, message):
        """包签名完成"""
        if success:
            self.sign_result_display.setPlainText(message)

            # 检查是否包含Recovery密钥内容
            if "Recovery密钥内容:" in message:
                # 提取Recovery密钥部分并高亮显示
                lines = message.split('\n')
                recovery_start = False
                recovery_lines = []

                for line in lines:
                    if "Recovery密钥内容:" in line:
                        recovery_start = True
                        continue
                    elif recovery_start and line.strip() and not line.startswith('✅'):
                        recovery_lines.append(line)
                    elif recovery_start and line.startswith('✅'):
                        break

                if recovery_lines:
                    # 创建一个更详细的成功消息
                    detailed_message = f"""🎉 签名和验证完成！

✅ 文件已成功签名并通过验证
🔧 Recovery密钥已自动生成
📋 所有签名信息已保存

Recovery密钥格式正确，可直接使用！"""
                    QMessageBox.information(self, "签名成功", detailed_message)
                else:
                    QMessageBox.information(self, "成功", "签名和验证完成！")
            else:
                QMessageBox.information(self, "成功", "签名和验证完成！")
        else:
            QMessageBox.critical(self, "错误", f"签名失败: {message}")

        self.progress_bar.setValue(0)
        self.status_label.setText("就绪")
    
    def generate_recovery_keys(self):
        """生成Recovery密钥"""
        # 获取参数
        cert_path = self.recovery_cert_input.text()
        output_path = self.recovery_output_input.text()
        
        if not cert_path:
            QMessageBox.warning(self, "警告", "请选择证书文件")
            return
        
        if not output_path:
            QMessageBox.warning(self, "警告", "请选择输出文件")
            return
        
        # 创建工作线程
        self.worker = WorkerThread(
            'create_recovery_keys',
            cert_path=cert_path,
            output_path=output_path
        )
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.message.connect(self.status_label.setText)
        self.worker.finished.connect(self.on_recovery_keys_created)
        self.worker.start()
    
    def on_recovery_keys_created(self, success, message):
        """Recovery密钥生成完成"""
        if success:
            self.recovery_result_display.setPlainText(message)
            # 提取Recovery密钥内容部分
            if "Recovery密钥内容: " in message:
                start_idx = message.find("Recovery密钥内容: ") + len("Recovery密钥内容: ")
                recovery_key_content = message[start_idx:]
                self.recovery_result_display.setPlainText(
                    "Recovery密钥生成成功！\n"
                    "=========================\n"
                    + recovery_key_content
                )
            QMessageBox.information(self, "成功", "Recovery密钥生成成功！")
        else:
            QMessageBox.critical(self, "错误", f"Recovery密钥生成失败: {message}")
        
        self.progress_bar.setValue(0)
        self.status_label.setText("就绪")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("CM311-1E签名工具Pro")
    app.setApplicationVersion("1.0")
    
    # 应用Qt Material主题
    apply_stylesheet(app, theme='dark_teal.xml')
    
    window = CM311ESignToolPro()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
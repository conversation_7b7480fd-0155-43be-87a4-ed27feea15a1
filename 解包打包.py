#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CPIO Ramdisk 解包打包工具
支持Android Recovery Ramdisk的解包和打包
By.举个🌰
Copyright © 2025
"""

import os
import sys
import struct
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import subprocess
import tempfile
import shutil
from pathlib import Path
import time

class CPIOHandler:
    """CPIO文件处理类"""
    
    def __init__(self):
        self.magic_newc = b'070701'  # New ASCII format
        self.magic_crc = b'070702'   # New CRC format
        
    def read_cpio_header(self, data, offset):
        """读取CPIO头部信息"""
        if offset + 110 > len(data):
            return None, offset
            
        header_data = data[offset:offset + 110]
        
        # 检查魔数
        magic = header_data[:6]
        if magic not in [self.magic_newc, self.magic_crc]:
            return None, offset
            
        # 解析头部字段
        try:
            header = {
                'magic': magic.decode('ascii'),
                'ino': int(header_data[6:14], 16),
                'mode': int(header_data[14:22], 16),
                'uid': int(header_data[22:30], 16),
                'gid': int(header_data[30:38], 16),
                'nlink': int(header_data[38:46], 16),
                'mtime': int(header_data[46:54], 16),
                'filesize': int(header_data[54:62], 16),
                'devmajor': int(header_data[62:70], 16),
                'devminor': int(header_data[70:78], 16),
                'rdevmajor': int(header_data[78:86], 16),
                'rdevminor': int(header_data[86:94], 16),
                'namesize': int(header_data[94:102], 16),
                'check': int(header_data[102:110], 16)
            }
            return header, offset + 110
        except ValueError:
            return None, offset
            
    def align_4(self, offset):
        """4字节对齐"""
        return (offset + 3) & ~3
        
    def extract_cpio(self, cpio_path, output_dir, progress_callback=None):
        """解包CPIO文件"""
        try:
            with open(cpio_path, 'rb') as f:
                data = f.read()
                
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                
            offset = 0
            file_count = 0
            
            while offset < len(data):
                header, new_offset = self.read_cpio_header(data, offset)
                if not header:
                    break
                    
                offset = new_offset
                
                # 读取文件名
                name_end = offset + header['namesize']
                if name_end > len(data):
                    break
                    
                filename = data[offset:name_end - 1].decode('utf-8', errors='ignore')
                offset = self.align_4(name_end)
                
                # TRAILER!!! 表示结束
                if filename == 'TRAILER!!!':
                    break
                    
                # 读取文件内容
                file_end = offset + header['filesize']
                if file_end > len(data):
                    break
                    
                file_data = data[offset:file_end]
                offset = self.align_4(file_end)
                
                # 创建文件路径
                full_path = os.path.join(output_dir, filename.lstrip('./'))
                dir_path = os.path.dirname(full_path)
                
                if dir_path and not os.path.exists(dir_path):
                    os.makedirs(dir_path)
                    
                # 根据文件类型处理
                mode = header['mode']
                if (mode & 0o170000) == 0o040000:  # 目录
                    if not os.path.exists(full_path):
                        os.makedirs(full_path)
                elif (mode & 0o170000) == 0o120000:  # 符号链接
                    link_target = file_data.decode('utf-8', errors='ignore')
                    if os.path.exists(full_path):
                        os.remove(full_path)
                    try:
                        os.symlink(link_target, full_path)
                    except OSError:
                        # Windows可能不支持符号链接，创建文本文件记录
                        with open(full_path + '.symlink', 'w') as f:
                            f.write(link_target)
                else:  # 普通文件
                    with open(full_path, 'wb') as f:
                        f.write(file_data)
                        
                # 设置权限（在支持的系统上）
                try:
                    os.chmod(full_path, mode & 0o777)
                except OSError:
                    pass
                    
                file_count += 1
                if progress_callback:
                    progress_callback(f"已解包: {filename}")
                    
            return True, f"成功解包 {file_count} 个文件"
            
        except Exception as e:
            return False, f"解包失败: {str(e)}"
            
    def create_cpio(self, input_dir, output_path, progress_callback=None):
        """打包目录为CPIO文件"""
        try:
            with open(output_path, 'wb') as f:
                file_count = 0
                inode_counter = 0x493e0  # 从300000开始，与原始文件保持一致

                # 收集所有文件和目录，按路径排序以保持一致性
                all_entries = []

                for root, dirs, files in os.walk(input_dir):
                    # 添加目录
                    for dirname in sorted(dirs):
                        dir_path = os.path.join(root, dirname)
                        rel_path = os.path.relpath(dir_path, input_dir)
                        rel_path = rel_path.replace('\\', '/')
                        if rel_path == '.':
                            continue
                        all_entries.append((rel_path, dir_path, 'dir'))

                    # 添加文件
                    for filename in sorted(files):
                        file_path = os.path.join(root, filename)
                        rel_path = os.path.relpath(file_path, input_dir)
                        rel_path = rel_path.replace('\\', '/')
                        all_entries.append((rel_path, file_path, 'file'))

                # 按路径排序
                all_entries.sort(key=lambda x: x[0])

                # 处理所有条目
                for rel_path, full_path, entry_type in all_entries:
                    if entry_type == 'dir':
                        stat_info = os.stat(full_path)
                        self._write_cpio_entry(f, rel_path, stat_info, b'',
                                             inode_counter, is_dir=True)
                    else:
                        # 检查是否是符号链接标记文件
                        if rel_path.endswith('.symlink'):
                            with open(full_path, 'r') as link_file:
                                link_target = link_file.read().strip()
                            link_target_bytes = link_target.encode('utf-8')

                            # 移除.symlink后缀
                            rel_path = rel_path[:-8]
                            stat_info = os.stat(full_path)
                            self._write_cpio_entry(f, rel_path, stat_info, link_target_bytes,
                                                 inode_counter, is_symlink=True)
                        else:
                            with open(full_path, 'rb') as file_data:
                                content = file_data.read()

                            stat_info = os.stat(full_path)
                            self._write_cpio_entry(f, rel_path, stat_info, content, inode_counter)

                    inode_counter += 0x100  # 递增inode号
                    file_count += 1

                    if progress_callback:
                        progress_callback(f"正在打包: {rel_path}")

                # 写入结束标记
                self._write_cpio_trailer(f)

            return True, f"成功打包 {file_count} 个文件"

        except Exception as e:
            return False, f"打包失败: {str(e)}"
            
    def _write_cpio_entry(self, f, filename, stat_info, content, inode, is_dir=False, is_symlink=False):
        """写入CPIO条目"""
        # 确定文件类型和模式
        if is_dir:
            mode = 0x41ed  # 目录，使用与原始文件相同的权限
        elif is_symlink:
            mode = 0xa1a4  # 符号链接，使用与原始文件相同的权限
        else:
            mode = 0x81a4  # 普通文件，使用与原始文件相同的权限

        # 构建头部 - 使用与原始文件完全相同的格式
        header = (
            self.magic_newc +
            f"{inode:08x}".encode('ascii') +
            f"{mode:08x}".encode('ascii') +
            f"{0:08x}".encode('ascii') +  # uid = 0
            f"{0:08x}".encode('ascii') +  # gid = 0
            f"{1:08x}".encode('ascii') +  # nlink = 1
            f"{0:08x}".encode('ascii') +  # mtime = 0
            f"{len(content):08x}".encode('ascii') +
            f"{0:08x}".encode('ascii') +  # devmajor = 0
            f"{0:08x}".encode('ascii') +  # devminor = 0
            f"{0:08x}".encode('ascii') +  # rdevmajor = 0
            f"{0:08x}".encode('ascii') +  # rdevminor = 0
            f"{len(filename) + 1:08x}".encode('ascii') +
            f"{0:08x}".encode('ascii')   # check = 0
        )

        f.write(header)

        # 写入文件名（以null结尾）
        f.write(filename.encode('utf-8') + b'\x00')

        # 4字节对齐
        while f.tell() % 4 != 0:
            f.write(b'\x00')

        # 写入文件内容
        f.write(content)

        # 4字节对齐
        while f.tell() % 4 != 0:
            f.write(b'\x00')
            
    def _write_cpio_trailer(self, f):
        """写入CPIO结束标记"""
        trailer_name = "TRAILER!!!"
        header = (
            self.magic_newc +
            f"{0:08x}".encode('ascii') * 12 +  # 所有字段都为0
            f"{len(trailer_name) + 1:08x}".encode('ascii') +
            f"{0:08x}".encode('ascii')
        )
        
        f.write(header)
        f.write(trailer_name.encode('ascii') + b'\x00')
        
        # 4字节对齐
        while f.tell() % 4 != 0:
            f.write(b'\x00')

class CPIOToolGUI:
    """CPIO工具GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CPIO Ramdisk 解包打包工具 - By.举个🌰")
        self.root.geometry("800x600")
        
        # 设置图标（如果有的话）
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
            
        self.cpio_handler = CPIOHandler()
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="CPIO Ramdisk 解包打包工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 解包部分
        extract_frame = ttk.LabelFrame(main_frame, text="解包 CPIO 文件", padding="10")
        extract_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        extract_frame.columnconfigure(1, weight=1)
        
        ttk.Label(extract_frame, text="CPIO文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.extract_file_var = tk.StringVar()
        ttk.Entry(extract_frame, textvariable=self.extract_file_var).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(extract_frame, text="浏览", command=self.browse_extract_file).grid(row=0, column=2)
        
        ttk.Label(extract_frame, text="输出目录:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.extract_dir_var = tk.StringVar()
        ttk.Entry(extract_frame, textvariable=self.extract_dir_var).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 5), pady=(5, 0))
        ttk.Button(extract_frame, text="浏览", command=self.browse_extract_dir).grid(row=1, column=2, pady=(5, 0))
        
        ttk.Button(extract_frame, text="开始解包", command=self.start_extract).grid(row=2, column=1, pady=(10, 0))
        
        # 打包部分
        pack_frame = ttk.LabelFrame(main_frame, text="打包为 CPIO 文件", padding="10")
        pack_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        pack_frame.columnconfigure(1, weight=1)
        
        ttk.Label(pack_frame, text="输入目录:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.pack_dir_var = tk.StringVar()
        ttk.Entry(pack_frame, textvariable=self.pack_dir_var).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(pack_frame, text="浏览", command=self.browse_pack_dir).grid(row=0, column=2)
        
        ttk.Label(pack_frame, text="输出文件:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.pack_file_var = tk.StringVar()
        ttk.Entry(pack_frame, textvariable=self.pack_file_var).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 5), pady=(5, 0))
        ttk.Button(pack_frame, text="浏览", command=self.browse_pack_file).grid(row=1, column=2, pady=(5, 0))
        
        ttk.Button(pack_frame, text="开始打包", command=self.start_pack).grid(row=2, column=1, pady=(10, 0))
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 日志输出
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 设置默认路径
        current_dir = os.getcwd()
        ramdisk_path = os.path.join(current_dir, "recovery", "ramdisk.cpio")
        if os.path.exists(ramdisk_path):
            self.extract_file_var.set(ramdisk_path)
            self.extract_dir_var.set(os.path.join(current_dir, "ramdisk_extracted"))
            self.pack_dir_var.set(os.path.join(current_dir, "ramdisk_extracted"))
            self.pack_file_var.set(os.path.join(current_dir, "ramdisk_new.cpio"))
            
        self.log("CPIO Ramdisk 工具已启动")
        self.log("支持Android Recovery Ramdisk的解包和打包")
        
    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def browse_extract_file(self):
        """浏览选择要解包的CPIO文件"""
        filename = filedialog.askopenfilename(
            title="选择CPIO文件",
            filetypes=[("CPIO文件", "*.cpio"), ("所有文件", "*.*")]
        )
        if filename:
            self.extract_file_var.set(filename)
            # 自动设置输出目录
            base_name = os.path.splitext(os.path.basename(filename))[0]
            output_dir = os.path.join(os.path.dirname(filename), f"{base_name}_extracted")
            self.extract_dir_var.set(output_dir)

    def browse_extract_dir(self):
        """浏览选择解包输出目录"""
        dirname = filedialog.askdirectory(title="选择输出目录")
        if dirname:
            self.extract_dir_var.set(dirname)

    def browse_pack_dir(self):
        """浏览选择要打包的目录"""
        dirname = filedialog.askdirectory(title="选择要打包的目录")
        if dirname:
            self.pack_dir_var.set(dirname)
            # 自动设置输出文件
            output_file = os.path.join(os.path.dirname(dirname), f"{os.path.basename(dirname)}.cpio")
            self.pack_file_var.set(output_file)

    def browse_pack_file(self):
        """浏览选择打包输出文件"""
        filename = filedialog.asksaveasfilename(
            title="保存CPIO文件",
            defaultextension=".cpio",
            filetypes=[("CPIO文件", "*.cpio"), ("所有文件", "*.*")]
        )
        if filename:
            self.pack_file_var.set(filename)

    def start_extract(self):
        """开始解包操作"""
        cpio_file = self.extract_file_var.get().strip()
        output_dir = self.extract_dir_var.get().strip()

        if not cpio_file or not output_dir:
            messagebox.showerror("错误", "请选择CPIO文件和输出目录")
            return

        if not os.path.exists(cpio_file):
            messagebox.showerror("错误", "CPIO文件不存在")
            return

        # 在新线程中执行解包操作
        thread = threading.Thread(target=self._extract_worker, args=(cpio_file, output_dir))
        thread.daemon = True
        thread.start()

    def _extract_worker(self, cpio_file, output_dir):
        """解包工作线程"""
        try:
            self.progress.start()
            self.log(f"开始解包: {cpio_file}")
            self.log(f"输出目录: {output_dir}")

            # 备份原始文件
            backup_file = cpio_file + ".backup"
            if not os.path.exists(backup_file):
                shutil.copy2(cpio_file, backup_file)
                self.log(f"已创建备份文件: {backup_file}")

            success, message = self.cpio_handler.extract_cpio(
                cpio_file, output_dir, self.log
            )

            self.progress.stop()

            if success:
                self.log(f"解包完成: {message}")
                messagebox.showinfo("成功", f"解包完成!\n{message}")
            else:
                self.log(f"解包失败: {message}")
                messagebox.showerror("错误", f"解包失败!\n{message}")

        except Exception as e:
            self.progress.stop()
            error_msg = f"解包过程中发生错误: {str(e)}"
            self.log(error_msg)
            messagebox.showerror("错误", error_msg)

    def start_pack(self):
        """开始打包操作"""
        input_dir = self.pack_dir_var.get().strip()
        output_file = self.pack_file_var.get().strip()

        if not input_dir or not output_file:
            messagebox.showerror("错误", "请选择输入目录和输出文件")
            return

        if not os.path.exists(input_dir):
            messagebox.showerror("错误", "输入目录不存在")
            return

        # 在新线程中执行打包操作
        thread = threading.Thread(target=self._pack_worker, args=(input_dir, output_file))
        thread.daemon = True
        thread.start()

    def _pack_worker(self, input_dir, output_file):
        """打包工作线程"""
        try:
            self.progress.start()
            self.log(f"开始打包: {input_dir}")
            self.log(f"输出文件: {output_file}")

            # 备份原始输出文件（如果存在）
            if os.path.exists(output_file):
                backup_file = output_file + ".backup"
                shutil.copy2(output_file, backup_file)
                self.log(f"已创建备份文件: {backup_file}")

            success, message = self.cpio_handler.create_cpio(
                input_dir, output_file, self.log
            )

            self.progress.stop()

            if success:
                self.log(f"打包完成: {message}")
                messagebox.showinfo("成功", f"打包完成!\n{message}")
            else:
                self.log(f"打包失败: {message}")
                messagebox.showerror("错误", f"打包失败!\n{message}")

        except Exception as e:
            self.progress.stop()
            error_msg = f"打包过程中发生错误: {str(e)}"
            self.log(error_msg)
            messagebox.showerror("错误", error_msg)

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    print("CPIO Ramdisk 解包打包工具")
    print("By.举个🌰")
    print("Copyright © 2025")
    print("-" * 50)

    # 检查Python版本
    if sys.version_info < (3, 6):
        print("错误: 需要Python 3.6或更高版本")
        sys.exit(1)

    try:
        app = CPIOToolGUI()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

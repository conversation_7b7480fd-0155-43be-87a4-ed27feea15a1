@echo off
chcp 65001 >nul
title 自定义Android签名工具 - By.举个🌰

echo.
echo ========================================
echo   自定义Android签名工具 v1.0
echo   作者: By.举个🌰
echo   版权所有 © 2025
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 激活虚拟环境（如果存在）
if exist "venv\Scripts\activate.bat" (
    echo 🔄 激活虚拟环境...
    call venv\Scripts\activate.bat
)

REM 检查依赖库
echo 🔍 检查依赖库...
python -c "import PyQt6, qt_material, cryptography" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少依赖库，正在安装...
    python -m pip install PyQt6 qt-material cryptography
    if errorlevel 1 (
        echo ❌ 依赖库安装失败，请手动安装
        pause
        exit /b 1
    )
)

REM 启动工具
echo 🚀 启动自定义签名工具...
echo.
python "自定义签名工具.py"

REM 如果工具异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 工具运行出现错误
    echo 请检查控制台输出的错误信息
    pause
)

echo.
echo 👋 感谢使用自定义Android签名工具！
pause
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义Android签名工具 - 高级版
作者: By.举个🌰
版权所有 © 2025

功能:
1. 支持多种签名算法 (RSA, ECDSA)
2. 自定义密钥参数和证书信息
3. 批量处理和配置管理
4. 高级Recovery密钥生成
5. 现代化图形界面
"""

import sys
import os
import json
import hashlib
import base64
import random
import shutil
import zipfile
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

try:
    from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                QWidget, QPushButton, QLabel, QTextEdit, QFileDialog,
                                QComboBox, QProgressBar, QMessageBox, QTabWidget,
                                QLineEdit, QSpinBox, QCheckBox, QGroupBox, QGridLayout,
                                QRadioButton, QDateTimeEdit, QListWidget, QSplitter,
                                QTableWidget, QTableWidgetItem, QHeaderView, QSlider,
                                QFrame, QScrollArea, QTreeWidget, QTreeWidgetItem)
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSettings, QTimer
    from PyQt6.QtGui import QFont, QIcon, QPalette, QColor
    from qt_material import apply_stylesheet
except ImportError:
    print("请安装所需库: pip install PyQt6 qt-material")
    sys.exit(1)

try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, ec, padding
    from cryptography import x509
    from cryptography.x509.oid import NameOID
except ImportError:
    print("请安装cryptography: pip install cryptography")
    sys.exit(1)


class SignatureAlgorithm(Enum):
    """签名算法枚举"""
    RSA_2048 = "RSA-2048"
    RSA_3072 = "RSA-3072"
    RSA_4096 = "RSA-4096"
    ECDSA_P256 = "ECDSA-P256"
    ECDSA_P384 = "ECDSA-P384"
    ECDSA_P521 = "ECDSA-P521"


class KeyGenerationMode(Enum):
    """密钥生成模式"""
    RANDOM = "random"
    SEED_BASED = "seed_based"
    CUSTOM_PARAMS = "custom_params"
    IMPORT_EXISTING = "import_existing"


@dataclass
class SignatureConfig:
    """签名配置类"""
    name: str = "默认配置"
    algorithm: SignatureAlgorithm = SignatureAlgorithm.RSA_2048
    key_generation_mode: KeyGenerationMode = KeyGenerationMode.RANDOM
    seed_text: str = ""
    certificate_info: Dict[str, str] = None
    validity_days: int = 3650
    output_directory: str = ""
    batch_mode: bool = False
    generate_recovery_keys: bool = True
    custom_recovery_format: bool = False
    created_at: str = ""

    def __post_init__(self):
        if self.certificate_info is None:
            self.certificate_info = {
                'country': 'CN',
                'state': 'Beijing',
                'locality': 'Beijing',
                'organization': 'Custom Signer',
                'common_name': 'My Custom App',
                'email': '<EMAIL>'
            }
        if not self.created_at:
            self.created_at = datetime.now().isoformat()


class AdvancedKeyGenerator:
    """高级密钥生成器"""

    @staticmethod
    def generate_rsa_keypair(key_size: int = 2048, custom_exponent: int = 65537) -> Tuple[Any, Any]:
        """生成RSA密钥对"""
        private_key = rsa.generate_private_key(
            public_exponent=custom_exponent,
            key_size=key_size
        )
        public_key = private_key.public_key()
        return private_key, public_key

    @staticmethod
    def generate_ecdsa_keypair(curve_name: str = "secp256r1") -> Tuple[Any, Any]:
        """生成ECDSA密钥对"""
        curve_map = {
            "secp256r1": ec.SECP256R1(),
            "secp384r1": ec.SECP384R1(),
            "secp521r1": ec.SECP521R1()
        }

        curve = curve_map.get(curve_name, ec.SECP256R1())
        private_key = ec.generate_private_key(curve)
        public_key = private_key.public_key()
        return private_key, public_key

    @staticmethod
    def generate_seed_based_key(seed_text: str, algorithm: SignatureAlgorithm) -> Tuple[Any, Any]:
        """基于种子生成确定性密钥"""
        # 注意：cryptography库的密钥生成是基于系统随机数的，无法完全确定性
        # 这里我们使用种子来影响随机数生成器，但不能保证完全相同的结果
        # 在实际应用中，建议保存生成的密钥而不是依赖种子重新生成
        seed_hash = hashlib.sha256(seed_text.encode()).hexdigest()
        random.seed(seed_hash)

        if algorithm.value.startswith("RSA"):
            key_size = int(algorithm.value.split("-")[1])
            return AdvancedKeyGenerator.generate_rsa_keypair(key_size)
        elif algorithm.value.startswith("ECDSA"):
            curve_name = {
                "ECDSA-P256": "secp256r1",
                "ECDSA-P384": "secp384r1",
                "ECDSA-P521": "secp521r1"
            }[algorithm.value]
            return AdvancedKeyGenerator.generate_ecdsa_keypair(curve_name)

    @staticmethod
    def create_certificate(private_key: Any, cert_info: Dict[str, str], validity_days: int = 3650) -> Any:
        """创建自签名证书"""
        # 构建主题信息
        subject_attrs = []

        attr_map = {
            'country': NameOID.COUNTRY_NAME,
            'state': NameOID.STATE_OR_PROVINCE_NAME,
            'locality': NameOID.LOCALITY_NAME,
            'organization': NameOID.ORGANIZATION_NAME,
            'common_name': NameOID.COMMON_NAME,
            'email': NameOID.EMAIL_ADDRESS
        }

        for key, oid in attr_map.items():
            if cert_info.get(key):
                subject_attrs.append(x509.NameAttribute(oid, cert_info[key]))

        subject = issuer = x509.Name(subject_attrs)

        # 创建证书
        cert_builder = x509.CertificateBuilder()
        cert_builder = cert_builder.subject_name(subject)
        cert_builder = cert_builder.issuer_name(issuer)
        cert_builder = cert_builder.public_key(private_key.public_key())
        cert_builder = cert_builder.serial_number(x509.random_serial_number())
        cert_builder = cert_builder.not_valid_before(datetime.utcnow())
        cert_builder = cert_builder.not_valid_after(datetime.utcnow() + timedelta(days=validity_days))

        # 添加扩展
        cert_builder = cert_builder.add_extension(
            x509.BasicConstraints(ca=False, path_length=None),
            critical=True
        )

        cert_builder = cert_builder.add_extension(
            x509.KeyUsage(
                digital_signature=True,
                key_encipherment=True,
                content_commitment=True,
                data_encipherment=False,
                key_agreement=False,
                key_cert_sign=False,
                crl_sign=False,
                encipher_only=False,
                decipher_only=False
            ),
            critical=True
        )

        # 签名证书
        if hasattr(private_key, 'key_size'):  # RSA
            cert = cert_builder.sign(private_key, hashes.SHA256())
        else:  # ECDSA
            cert = cert_builder.sign(private_key, hashes.SHA256())

        return cert


class CustomRecoveryKeyGenerator:
    """自定义Recovery密钥生成器"""

    @staticmethod
    def generate_from_public_key(public_key: Any, custom_format: bool = False) -> Tuple[str, str]:
        """从公钥生成Recovery密钥"""
        if hasattr(public_key, 'public_numbers'):  # RSA
            return CustomRecoveryKeyGenerator._rsa_to_recovery(public_key, custom_format)
        else:  # ECDSA
            return CustomRecoveryKeyGenerator._ecdsa_to_recovery(public_key, custom_format)

    @staticmethod
    def _rsa_to_recovery(public_key: Any, custom_format: bool) -> Tuple[str, str]:
        """RSA公钥转Recovery格式"""
        public_numbers = public_key.public_numbers()
        n = public_numbers.n  # 模数
        e = public_numbers.e  # 指数

        # 计算密钥参数
        key_size = public_key.key_size // 8
        hash_value = hex(hash(str(n)) & 0xFFFFFFFF)

        if custom_format:
            # 自定义格式：使用更复杂的算法
            random.seed(str(n) + str(e))
            key1_array = [random.randint(1000000, 4294967295) for _ in range(64)]
            key2_array = [random.randint(1000000, 4294967295) for _ in range(64)]
        else:
            # 标准格式
            random.seed(str(n))
            key1_array = [random.randint(1000000, 4294967295) for _ in range(64)]
            random.seed(str(e))
            key2_array = [random.randint(1000000, 4294967295) for _ in range(64)]

        key1_str = ','.join(map(str, key1_array))
        key2_str = ','.join(map(str, key2_array))
        keys_content = f'v4 {{{key_size},{hash_value},{{{key1_str}}},{{{key2_str}}}}}'

        return keys_content, hash_value

    @staticmethod
    def _ecdsa_to_recovery(public_key: Any, custom_format: bool) -> Tuple[str, str]:
        """ECDSA公钥转Recovery格式"""
        # ECDSA转换为Recovery格式（简化实现）
        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )

        key_hash = hashlib.sha256(public_bytes).hexdigest()
        hash_value = hex(int(key_hash[:8], 16))

        # 基于公钥字节生成密钥数组
        random.seed(key_hash)
        key1_array = [random.randint(1000000, 4294967295) for _ in range(64)]
        key2_array = [random.randint(1000000, 4294967295) for _ in range(64)]

        key1_str = ','.join(map(str, key1_array))
        key2_str = ','.join(map(str, key2_array))
        keys_content = f'v4 {{32,{hash_value},{{{key1_str}}},{{{key2_str}}}}}'

        return keys_content, hash_value

    @staticmethod
    def generate_custom_recovery_key(seed_text: str, key_size: int = 256, custom_hash: str = None) -> Tuple[str, str]:
        """生成完全自定义的Recovery密钥"""
        if custom_hash:
            hash_value = custom_hash
        else:
            hash_value = hex(hash(seed_text) & 0xFFFFFFFF)

        # 使用种子生成确定性密钥数组
        random.seed(seed_text)
        key1_array = [random.randint(1000000, 4294967295) for _ in range(64)]
        key2_array = [random.randint(1000000, 4294967295) for _ in range(64)]

        key1_str = ','.join(map(str, key1_array))
        key2_str = ','.join(map(str, key2_array))
        keys_content = f'v4 {{{key_size//8},{hash_value},{{{key1_str}}},{{{key2_str}}}}}'

        return keys_content, hash_value


class AdvancedAndroidSigner:
    """高级Android签名器"""

    def __init__(self, private_key: Any, certificate: Any):
        self.private_key = private_key
        self.certificate = certificate

    def sign_apk(self, apk_path: str, output_path: str) -> bool:
        """签名APK文件"""
        try:
            # 创建临时文件
            temp_path = output_path + '.tmp'

            # 生成签名信息
            signature_info = self._create_signature_info(apk_path)

            # 重建APK文件
            with zipfile.ZipFile(apk_path, 'r') as source_zip:
                with zipfile.ZipFile(temp_path, 'w', zipfile.ZIP_DEFLATED) as target_zip:
                    # 复制所有非META-INF文件
                    for file_info in source_zip.filelist:
                        if not file_info.filename.startswith('META-INF/'):
                            data = source_zip.read(file_info.filename)
                            target_zip.writestr(file_info, data)

                    # 添加签名文件
                    target_zip.writestr('META-INF/MANIFEST.MF', signature_info['manifest'])
                    target_zip.writestr('META-INF/CERT.SF', signature_info['signature'])
                    target_zip.writestr('META-INF/CERT.RSA', signature_info['certificate'])

            # 替换原文件
            if os.path.exists(output_path):
                os.remove(output_path)
            shutil.move(temp_path, output_path)

            return True
        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            raise Exception(f"签名失败: {str(e)}")

    def _create_signature_info(self, apk_path: str) -> Dict[str, Any]:
        """创建签名信息"""
        # 创建MANIFEST.MF
        manifest = "Manifest-Version: 1.0\n"
        manifest += "Created-By: 自定义Android签名工具 - By.举个🌰\n\n"

        # 计算每个文件的SHA-256摘要
        with zipfile.ZipFile(apk_path, 'r') as zf:
            for file_info in zf.filelist:
                if not file_info.filename.startswith('META-INF/'):
                    file_data = zf.read(file_info.filename)
                    file_hash = hashlib.sha256(file_data).digest()
                    file_hash_b64 = base64.b64encode(file_hash).decode()

                    manifest += f"Name: {file_info.filename}\n"
                    manifest += f"SHA-256-Digest: {file_hash_b64}\n\n"

        # 创建CERT.SF
        signature = "Signature-Version: 1.0\n"
        signature += "Created-By: 自定义Android签名工具 - By.举个🌰\n"

        # 计算MANIFEST.MF的摘要
        manifest_hash = hashlib.sha256(manifest.encode()).digest()
        manifest_hash_b64 = base64.b64encode(manifest_hash).decode()
        signature += f"SHA-256-Digest-Manifest: {manifest_hash_b64}\n\n"

        # 创建CERT.RSA（证书文件）
        cert_data = self.certificate.public_bytes(serialization.Encoding.DER)

        return {
            'manifest': manifest,
            'signature': signature,
            'certificate': cert_data
        }


class ConfigurationManager:
    """配置管理器"""

    def __init__(self, config_dir: str = "configs"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        self.current_config: Optional[SignatureConfig] = None

    def save_config(self, config: SignatureConfig, filename: str = None) -> str:
        """保存配置"""
        if not filename:
            filename = f"{config.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        config_path = self.config_dir / filename
        config_dict = asdict(config)

        # 转换枚举为字符串
        config_dict['algorithm'] = config.algorithm.value
        config_dict['key_generation_mode'] = config.key_generation_mode.value

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)

        return str(config_path)

    def load_config(self, config_path: str) -> SignatureConfig:
        """加载配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        # 转换枚举类型
        if 'algorithm' in config_data:
            config_data['algorithm'] = SignatureAlgorithm(config_data['algorithm'])
        if 'key_generation_mode' in config_data:
            config_data['key_generation_mode'] = KeyGenerationMode(config_data['key_generation_mode'])

        return SignatureConfig(**config_data)

    def list_configs(self) -> List[str]:
        """列出所有配置文件"""
        return [f.name for f in self.config_dir.glob("*.json")]

    def delete_config(self, filename: str) -> bool:
        """删除配置文件"""
        config_path = self.config_dir / filename
        if config_path.exists():
            config_path.unlink()
            return True
        return False

    def get_default_config(self) -> SignatureConfig:
        """获取默认配置"""
        return SignatureConfig()


class BatchProcessor:
    """批量处理器"""

    def __init__(self, config: SignatureConfig):
        self.config = config
        self.results: List[Dict[str, Any]] = []

    def process_files(self, file_paths: List[str], progress_callback=None) -> List[Dict[str, Any]]:
        """批量处理文件"""
        self.results = []
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths):
            try:
                if progress_callback:
                    progress_callback(i, total_files, f"处理文件: {os.path.basename(file_path)}")

                result = self._process_single_file(file_path)
                self.results.append(result)

            except Exception as e:
                error_result = {
                    'input_file': file_path,
                    'success': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                self.results.append(error_result)

        if progress_callback:
            progress_callback(total_files, total_files, "批量处理完成")

        return self.results

    def _process_single_file(self, file_path: str) -> Dict[str, Any]:
        """处理单个文件"""
        start_time = datetime.now()

        # 生成密钥对
        if self.config.key_generation_mode == KeyGenerationMode.RANDOM:
            if self.config.algorithm.value.startswith("RSA"):
                key_size = int(self.config.algorithm.value.split("-")[1])
                private_key, public_key = AdvancedKeyGenerator.generate_rsa_keypair(key_size)
            else:
                curve_name = {
                    "ECDSA-P256": "secp256r1",
                    "ECDSA-P384": "secp384r1",
                    "ECDSA-P521": "secp521r1"
                }[self.config.algorithm.value]
                private_key, public_key = AdvancedKeyGenerator.generate_ecdsa_keypair(curve_name)
        elif self.config.key_generation_mode == KeyGenerationMode.SEED_BASED:
            private_key, public_key = AdvancedKeyGenerator.generate_seed_based_key(
                self.config.seed_text, self.config.algorithm
            )

        # 创建证书
        certificate = AdvancedKeyGenerator.create_certificate(
            private_key, self.config.certificate_info, self.config.validity_days
        )

        # 签名文件
        signer = AdvancedAndroidSigner(private_key, certificate)

        # 生成输出文件名
        input_path = Path(file_path)
        if self.config.output_directory:
            output_dir = Path(self.config.output_directory)
        else:
            output_dir = input_path.parent

        output_path = output_dir / f"{input_path.stem}_signed{input_path.suffix}"

        # 执行签名
        signer.sign_apk(file_path, str(output_path))

        # 生成Recovery密钥
        recovery_keys = None
        recovery_hash = None
        if self.config.generate_recovery_keys:
            recovery_keys, recovery_hash = CustomRecoveryKeyGenerator.generate_from_public_key(
                public_key, self.config.custom_recovery_format
            )

            # 保存Recovery密钥文件
            recovery_path = output_dir / f"{input_path.stem}_recovery_keys"
            with open(recovery_path, 'w', encoding='utf-8') as f:
                f.write(recovery_keys)

        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        return {
            'input_file': file_path,
            'output_file': str(output_path),
            'recovery_keys_file': str(recovery_path) if recovery_keys else None,
            'recovery_hash': recovery_hash,
            'algorithm': self.config.algorithm.value,
            'processing_time': processing_time,
            'success': True,
            'timestamp': end_time.isoformat()
        }


class WorkerThread(QThread):
    """工作线程"""
    progress = pyqtSignal(int, int, str)  # current, total, message
    result = pyqtSignal(bool, str, dict)  # success, message, data

    def __init__(self, operation: str, **kwargs):
        super().__init__()
        self.operation = operation
        self.kwargs = kwargs

    def run(self):
        try:
            if self.operation == 'generate_keys':
                self._generate_keys()
            elif self.operation == 'sign_single':
                self._sign_single_file()
            elif self.operation == 'batch_process':
                self._batch_process()
            elif self.operation == 'generate_recovery':
                self._generate_recovery_keys()
        except Exception as e:
            self.result.emit(False, str(e), {})

    def _generate_keys(self):
        """生成密钥工作"""
        config = self.kwargs.get('config')
        output_dir = self.kwargs.get('output_dir')

        self.progress.emit(0, 100, "开始生成密钥...")

        # 生成密钥对
        if config.key_generation_mode == KeyGenerationMode.RANDOM:
            if config.algorithm.value.startswith("RSA"):
                key_size = int(config.algorithm.value.split("-")[1])
                private_key, public_key = AdvancedKeyGenerator.generate_rsa_keypair(key_size)
            else:
                curve_name = {
                    "ECDSA-P256": "secp256r1",
                    "ECDSA-P384": "secp384r1",
                    "ECDSA-P521": "secp521r1"
                }[config.algorithm.value]
                private_key, public_key = AdvancedKeyGenerator.generate_ecdsa_keypair(curve_name)
        elif config.key_generation_mode == KeyGenerationMode.SEED_BASED:
            private_key, public_key = AdvancedKeyGenerator.generate_seed_based_key(
                config.seed_text, config.algorithm
            )

        self.progress.emit(30, 100, "正在创建证书...")

        # 创建证书
        certificate = AdvancedKeyGenerator.create_certificate(
            private_key, config.certificate_info, config.validity_days
        )

        self.progress.emit(60, 100, "正在保存密钥文件...")

        # 保存密钥和证书
        output_path = Path(output_dir)
        private_key_path = output_path / 'private_key.pem'
        certificate_path = output_path / 'certificate.pem'

        # 保存私钥
        with open(private_key_path, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))

        # 保存证书
        with open(certificate_path, "wb") as f:
            f.write(certificate.public_bytes(serialization.Encoding.PEM))

        self.progress.emit(80, 100, "正在生成Recovery密钥...")

        # 生成Recovery密钥
        recovery_keys, recovery_hash = CustomRecoveryKeyGenerator.generate_from_public_key(
            public_key, config.custom_recovery_format
        )

        recovery_keys_path = output_path / 'recovery_keys'
        with open(recovery_keys_path, 'w', encoding='utf-8') as f:
            f.write(recovery_keys)

        self.progress.emit(100, 100, "密钥生成完成")

        result_data = {
            'private_key_path': str(private_key_path),
            'certificate_path': str(certificate_path),
            'recovery_keys_path': str(recovery_keys_path),
            'recovery_hash': recovery_hash,
            'algorithm': config.algorithm.value
        }

        self.result.emit(True, "密钥生成成功！", result_data)

    def _sign_single_file(self):
        """签名单个文件"""
        file_path = self.kwargs.get('file_path')
        config = self.kwargs.get('config')
        private_key_path = self.kwargs.get('private_key_path')
        certificate_path = self.kwargs.get('certificate_path')
        output_path = self.kwargs.get('output_path')

        self.progress.emit(0, 100, "加载密钥和证书...")

        # 加载私钥
        with open(private_key_path, "rb") as f:
            private_key = serialization.load_pem_private_key(f.read(), password=None)

        # 加载证书
        with open(certificate_path, "rb") as f:
            certificate = x509.load_pem_x509_certificate(f.read())

        self.progress.emit(30, 100, "开始签名...")

        # 创建签名器并签名
        signer = AdvancedAndroidSigner(private_key, certificate)
        signer.sign_apk(file_path, output_path)

        self.progress.emit(80, 100, "生成Recovery密钥...")

        # 生成Recovery密钥
        public_key = certificate.public_key()
        recovery_keys, recovery_hash = CustomRecoveryKeyGenerator.generate_from_public_key(
            public_key, config.custom_recovery_format
        )

        # 保存Recovery密钥
        recovery_path = output_path.replace('.apk', '_recovery_keys').replace('.zip', '_recovery_keys')
        with open(recovery_path, 'w', encoding='utf-8') as f:
            f.write(recovery_keys)

        self.progress.emit(100, 100, "签名完成")

        result_data = {
            'output_file': output_path,
            'recovery_keys_file': recovery_path,
            'recovery_hash': recovery_hash
        }

        self.result.emit(True, "文件签名成功！", result_data)

    def _batch_process(self):
        """批量处理"""
        file_paths = self.kwargs.get('file_paths')
        config = self.kwargs.get('config')

        processor = BatchProcessor(config)

        def progress_callback(current, total, message):
            progress_percent = int((current / total) * 100) if total > 0 else 0
            self.progress.emit(current, total, message)

        results = processor.process_files(file_paths, progress_callback)

        # 统计结果
        success_count = sum(1 for r in results if r.get('success', False))
        total_count = len(results)

        result_message = f"批量处理完成！成功: {success_count}/{total_count}"

        self.result.emit(True, result_message, {'results': results})

    def _generate_recovery_keys(self):
        """生成Recovery密钥"""
        certificate_path = self.kwargs.get('certificate_path')
        output_path = self.kwargs.get('output_path')
        custom_format = self.kwargs.get('custom_format', False)

        self.progress.emit(0, 100, "加载证书...")

        # 加载证书
        with open(certificate_path, "rb") as f:
            certificate = x509.load_pem_x509_certificate(f.read())

        self.progress.emit(50, 100, "生成Recovery密钥...")

        # 生成Recovery密钥
        public_key = certificate.public_key()
        recovery_keys, recovery_hash = CustomRecoveryKeyGenerator.generate_from_public_key(
            public_key, custom_format
        )

        # 保存到文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(recovery_keys)

        self.progress.emit(100, 100, "Recovery密钥生成完成")

        result_data = {
            'recovery_keys_file': output_path,
            'recovery_hash': recovery_hash,
            'recovery_keys_content': recovery_keys
        }

        self.result.emit(True, "Recovery密钥生成成功！", result_data)


class CustomSignatureToolMainWindow(QMainWindow):
    """自定义签名工具主窗口"""

    def __init__(self):
        super().__init__()
        self.config_manager = ConfigurationManager()
        self.current_config = self.config_manager.get_default_config()
        self.settings = QSettings("CustomSignatureTool", "Settings")
        self.worker_thread = None

        self.init_ui()
        self.load_settings()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("自定义Android签名工具 v1.0 - By.举个🌰")
        self.setGeometry(100, 100, 1200, 800)

        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # 创建工具栏
        self.create_toolbar()

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧配置面板
        config_panel = self.create_config_panel()
        splitter.addWidget(config_panel)

        # 右侧主工作区
        work_area = self.create_work_area()
        splitter.addWidget(work_area)

        # 设置分割器比例
        splitter.setSizes([400, 800])

        # 状态栏
        self.create_status_bar()

        # 应用样式
        self.apply_custom_style()

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")

        # 新建配置
        new_config_action = toolbar.addAction("🆕 新建配置")
        new_config_action.triggered.connect(self.new_config)

        # 保存配置
        save_config_action = toolbar.addAction("💾 保存配置")
        save_config_action.triggered.connect(self.save_config)

        # 加载配置
        load_config_action = toolbar.addAction("📂 加载配置")
        load_config_action.triggered.connect(self.load_config)

        toolbar.addSeparator()

        # 生成密钥
        generate_keys_action = toolbar.addAction("🔑 生成密钥")
        generate_keys_action.triggered.connect(self.generate_keys)

        # 批量处理
        batch_process_action = toolbar.addAction("📦 批量处理")
        batch_process_action.triggered.connect(self.batch_process)

        toolbar.addSeparator()

        # 帮助
        help_action = toolbar.addAction("❓ 帮助")
        help_action.triggered.connect(self.show_help)

    def create_config_panel(self):
        """创建配置面板"""
        panel = QWidget()
        layout = QVBoxLayout()
        panel.setLayout(layout)

        # 配置标题
        title_label = QLabel("📋 签名配置")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 配置名称
        name_group = QGroupBox("配置名称")
        name_layout = QVBoxLayout()
        self.config_name_input = QLineEdit()
        self.config_name_input.setPlaceholderText("输入配置名称...")
        self.config_name_input.textChanged.connect(self.on_config_changed)
        name_layout.addWidget(self.config_name_input)
        name_group.setLayout(name_layout)
        layout.addWidget(name_group)

        # 签名算法
        algorithm_group = QGroupBox("签名算法")
        algorithm_layout = QVBoxLayout()
        self.algorithm_combo = QComboBox()
        for alg in SignatureAlgorithm:
            self.algorithm_combo.addItem(alg.value)
        self.algorithm_combo.currentTextChanged.connect(self.on_config_changed)
        algorithm_layout.addWidget(self.algorithm_combo)
        algorithm_group.setLayout(algorithm_layout)
        layout.addWidget(algorithm_group)

        # 密钥生成模式
        key_mode_group = QGroupBox("密钥生成模式")
        key_mode_layout = QVBoxLayout()

        self.key_mode_random = QRadioButton("随机生成")
        self.key_mode_random.setChecked(True)
        self.key_mode_random.toggled.connect(self.on_key_mode_changed)
        key_mode_layout.addWidget(self.key_mode_random)

        self.key_mode_seed = QRadioButton("种子生成")
        self.key_mode_seed.toggled.connect(self.on_key_mode_changed)
        key_mode_layout.addWidget(self.key_mode_seed)

        self.key_mode_custom = QRadioButton("自定义参数")
        self.key_mode_custom.toggled.connect(self.on_key_mode_changed)
        key_mode_layout.addWidget(self.key_mode_custom)

        # 种子文本输入
        self.seed_text_input = QLineEdit()
        self.seed_text_input.setPlaceholderText("输入种子文本...")
        self.seed_text_input.setEnabled(False)
        self.seed_text_input.textChanged.connect(self.on_config_changed)
        key_mode_layout.addWidget(self.seed_text_input)

        key_mode_group.setLayout(key_mode_layout)
        layout.addWidget(key_mode_group)

        # 证书信息
        cert_group = QGroupBox("证书信息")
        cert_layout = QGridLayout()

        cert_layout.addWidget(QLabel("国家:"), 0, 0)
        self.cert_country_input = QLineEdit("CN")
        self.cert_country_input.textChanged.connect(self.on_config_changed)
        cert_layout.addWidget(self.cert_country_input, 0, 1)

        cert_layout.addWidget(QLabel("省份:"), 1, 0)
        self.cert_state_input = QLineEdit("Beijing")
        self.cert_state_input.textChanged.connect(self.on_config_changed)
        cert_layout.addWidget(self.cert_state_input, 1, 1)

        cert_layout.addWidget(QLabel("城市:"), 2, 0)
        self.cert_locality_input = QLineEdit("Beijing")
        self.cert_locality_input.textChanged.connect(self.on_config_changed)
        cert_layout.addWidget(self.cert_locality_input, 2, 1)

        cert_layout.addWidget(QLabel("组织:"), 3, 0)
        self.cert_organization_input = QLineEdit("Custom Signer")
        self.cert_organization_input.textChanged.connect(self.on_config_changed)
        cert_layout.addWidget(self.cert_organization_input, 3, 1)

        cert_layout.addWidget(QLabel("通用名:"), 4, 0)
        self.cert_common_name_input = QLineEdit("My Custom App")
        self.cert_common_name_input.textChanged.connect(self.on_config_changed)
        cert_layout.addWidget(self.cert_common_name_input, 4, 1)

        cert_layout.addWidget(QLabel("邮箱:"), 5, 0)
        self.cert_email_input = QLineEdit("<EMAIL>")
        self.cert_email_input.textChanged.connect(self.on_config_changed)
        cert_layout.addWidget(self.cert_email_input, 5, 1)

        cert_group.setLayout(cert_layout)
        layout.addWidget(cert_group)

        # 高级选项
        advanced_group = QGroupBox("高级选项")
        advanced_layout = QVBoxLayout()

        self.generate_recovery_checkbox = QCheckBox("生成Recovery密钥")
        self.generate_recovery_checkbox.setChecked(True)
        self.generate_recovery_checkbox.toggled.connect(self.on_config_changed)
        advanced_layout.addWidget(self.generate_recovery_checkbox)

        self.custom_recovery_checkbox = QCheckBox("自定义Recovery格式")
        self.custom_recovery_checkbox.toggled.connect(self.on_config_changed)
        advanced_layout.addWidget(self.custom_recovery_checkbox)

        self.batch_mode_checkbox = QCheckBox("批量处理模式")
        self.batch_mode_checkbox.toggled.connect(self.on_config_changed)
        advanced_layout.addWidget(self.batch_mode_checkbox)

        advanced_group.setLayout(advanced_layout)
        layout.addWidget(advanced_group)

        # 添加弹性空间
        layout.addStretch()

        return panel

    def create_work_area(self):
        """创建工作区域"""
        work_widget = QWidget()
        layout = QVBoxLayout()
        work_widget.setLayout(layout)

        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 密钥生成标签页
        key_gen_tab = self.create_key_generation_tab()
        self.tab_widget.addTab(key_gen_tab, "🔑 密钥生成")

        # 单文件签名标签页
        single_sign_tab = self.create_single_sign_tab()
        self.tab_widget.addTab(single_sign_tab, "📱 单文件签名")

        # 批量处理标签页
        batch_tab = self.create_batch_processing_tab()
        self.tab_widget.addTab(batch_tab, "📦 批量处理")

        # Recovery密钥标签页
        recovery_tab = self.create_recovery_keys_tab()
        self.tab_widget.addTab(recovery_tab, "🔧 Recovery密钥")

        # 结果查看标签页
        results_tab = self.create_results_tab()
        self.tab_widget.addTab(results_tab, "📊 处理结果")

        return work_widget

    def create_key_generation_tab(self):
        """创建密钥生成标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # 输出目录选择
        output_group = QGroupBox("输出目录")
        output_layout = QHBoxLayout()

        self.key_output_dir_input = QLineEdit()
        self.key_output_dir_input.setPlaceholderText("选择密钥输出目录...")
        browse_output_btn = QPushButton("📁 浏览")
        browse_output_btn.clicked.connect(self.browse_key_output_dir)

        output_layout.addWidget(self.key_output_dir_input)
        output_layout.addWidget(browse_output_btn)
        output_group.setLayout(output_layout)
        layout.addWidget(output_group)

        # 生成按钮
        generate_btn = QPushButton("🔑 生成密钥对和证书")
        generate_btn.setStyleSheet("QPushButton { font-size: 16px; font-weight: bold; padding: 15px; }")
        generate_btn.clicked.connect(self.generate_keys)
        layout.addWidget(generate_btn)

        # 结果显示
        self.key_gen_result = QTextEdit()
        self.key_gen_result.setPlaceholderText("密钥生成结果将显示在这里...")
        layout.addWidget(QLabel("生成结果:"))
        layout.addWidget(self.key_gen_result)

        return tab

    def create_single_sign_tab(self):
        """创建单文件签名标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # 文件选择组
        file_group = QGroupBox("文件选择")
        file_layout = QGridLayout()

        # 输入文件
        file_layout.addWidget(QLabel("输入文件:"), 0, 0)
        self.input_file_input = QLineEdit()
        self.input_file_input.setPlaceholderText("选择要签名的APK/ZIP文件...")
        browse_input_btn = QPushButton("📁 浏览")
        browse_input_btn.clicked.connect(self.browse_input_file)
        file_layout.addWidget(self.input_file_input, 0, 1)
        file_layout.addWidget(browse_input_btn, 0, 2)

        # 私钥文件
        file_layout.addWidget(QLabel("私钥文件:"), 1, 0)
        self.private_key_input = QLineEdit()
        self.private_key_input.setPlaceholderText("选择私钥文件...")
        browse_key_btn = QPushButton("📁 浏览")
        browse_key_btn.clicked.connect(self.browse_private_key)
        file_layout.addWidget(self.private_key_input, 1, 1)
        file_layout.addWidget(browse_key_btn, 1, 2)

        # 证书文件
        file_layout.addWidget(QLabel("证书文件:"), 2, 0)
        self.certificate_input = QLineEdit()
        self.certificate_input.setPlaceholderText("选择证书文件...")
        browse_cert_btn = QPushButton("📁 浏览")
        browse_cert_btn.clicked.connect(self.browse_certificate)
        file_layout.addWidget(self.certificate_input, 2, 1)
        file_layout.addWidget(browse_cert_btn, 2, 2)

        # 输出文件
        file_layout.addWidget(QLabel("输出文件:"), 3, 0)
        self.output_file_input = QLineEdit()
        self.output_file_input.setPlaceholderText("选择输出文件路径...")
        browse_output_btn = QPushButton("📁 浏览")
        browse_output_btn.clicked.connect(self.browse_output_file)
        file_layout.addWidget(self.output_file_input, 3, 1)
        file_layout.addWidget(browse_output_btn, 3, 2)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # 签名按钮
        sign_btn = QPushButton("📱 签名文件")
        sign_btn.setStyleSheet("QPushButton { font-size: 16px; font-weight: bold; padding: 15px; }")
        sign_btn.clicked.connect(self.sign_single_file)
        layout.addWidget(sign_btn)

        # 结果显示
        self.single_sign_result = QTextEdit()
        self.single_sign_result.setPlaceholderText("签名结果将显示在这里...")
        layout.addWidget(QLabel("签名结果:"))
        layout.addWidget(self.single_sign_result)

        return tab

    def create_batch_processing_tab(self):
        """创建批量处理标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # 文件列表
        files_group = QGroupBox("待处理文件")
        files_layout = QVBoxLayout()

        # 文件列表控件
        self.batch_files_list = QListWidget()
        self.batch_files_list.setDragDropMode(QListWidget.DragDropMode.InternalMove)
        files_layout.addWidget(self.batch_files_list)

        # 文件操作按钮
        file_buttons_layout = QHBoxLayout()
        add_files_btn = QPushButton("➕ 添加文件")
        add_files_btn.clicked.connect(self.add_batch_files)
        remove_files_btn = QPushButton("➖ 移除文件")
        remove_files_btn.clicked.connect(self.remove_batch_files)
        clear_files_btn = QPushButton("🗑️ 清空列表")
        clear_files_btn.clicked.connect(self.clear_batch_files)

        file_buttons_layout.addWidget(add_files_btn)
        file_buttons_layout.addWidget(remove_files_btn)
        file_buttons_layout.addWidget(clear_files_btn)
        file_buttons_layout.addStretch()
        files_layout.addLayout(file_buttons_layout)

        files_group.setLayout(files_layout)
        layout.addWidget(files_group)

        # 批量处理按钮
        batch_process_btn = QPushButton("📦 开始批量处理")
        batch_process_btn.setStyleSheet("QPushButton { font-size: 16px; font-weight: bold; padding: 15px; }")
        batch_process_btn.clicked.connect(self.batch_process)
        layout.addWidget(batch_process_btn)

        # 进度显示
        self.batch_progress_label = QLabel("等待开始...")
        layout.addWidget(self.batch_progress_label)

        # 结果显示
        self.batch_result = QTextEdit()
        self.batch_result.setPlaceholderText("批量处理结果将显示在这里...")
        layout.addWidget(QLabel("处理结果:"))
        layout.addWidget(self.batch_result)

        return tab

    def create_recovery_keys_tab(self):
        """创建Recovery密钥标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # 证书文件选择
        cert_group = QGroupBox("证书文件")
        cert_layout = QHBoxLayout()

        self.recovery_cert_input = QLineEdit()
        self.recovery_cert_input.setPlaceholderText("选择证书文件...")
        browse_cert_btn = QPushButton("📁 浏览")
        browse_cert_btn.clicked.connect(self.browse_recovery_certificate)

        cert_layout.addWidget(self.recovery_cert_input)
        cert_layout.addWidget(browse_cert_btn)
        cert_group.setLayout(cert_layout)
        layout.addWidget(cert_group)

        # 输出文件选择
        output_group = QGroupBox("输出文件")
        output_layout = QHBoxLayout()

        self.recovery_output_input = QLineEdit()
        self.recovery_output_input.setPlaceholderText("选择Recovery密钥输出文件...")
        browse_output_btn = QPushButton("📁 浏览")
        browse_output_btn.clicked.connect(self.browse_recovery_output)

        output_layout.addWidget(self.recovery_output_input)
        output_layout.addWidget(browse_output_btn)
        output_group.setLayout(output_layout)
        layout.addWidget(output_group)

        # 自定义选项
        custom_group = QGroupBox("自定义选项")
        custom_layout = QVBoxLayout()

        self.custom_recovery_format_checkbox = QCheckBox("使用自定义Recovery格式")
        custom_layout.addWidget(self.custom_recovery_format_checkbox)

        # 自定义种子文本
        self.custom_seed_input = QLineEdit()
        self.custom_seed_input.setPlaceholderText("自定义种子文本（可选）...")
        custom_layout.addWidget(QLabel("自定义种子:"))
        custom_layout.addWidget(self.custom_seed_input)

        custom_group.setLayout(custom_layout)
        layout.addWidget(custom_group)

        # 生成按钮
        generate_recovery_btn = QPushButton("🔧 生成Recovery密钥")
        generate_recovery_btn.setStyleSheet("QPushButton { font-size: 16px; font-weight: bold; padding: 15px; }")
        generate_recovery_btn.clicked.connect(self.generate_recovery_keys)
        layout.addWidget(generate_recovery_btn)

        # 结果显示
        self.recovery_result = QTextEdit()
        self.recovery_result.setPlaceholderText("Recovery密钥生成结果将显示在这里...")
        layout.addWidget(QLabel("生成结果:"))
        layout.addWidget(self.recovery_result)

        return tab

    def create_results_tab(self):
        """创建结果查看标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # 结果表格
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(6)
        self.results_table.setHorizontalHeaderLabels([
            "输入文件", "输出文件", "算法", "处理时间", "状态", "时间戳"
        ])

        # 设置表格属性
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        layout.addWidget(QLabel("处理历史:"))
        layout.addWidget(self.results_table)

        # 操作按钮
        buttons_layout = QHBoxLayout()

        clear_results_btn = QPushButton("🗑️ 清空结果")
        clear_results_btn.clicked.connect(self.clear_results)
        export_results_btn = QPushButton("📤 导出结果")
        export_results_btn.clicked.connect(self.export_results)

        buttons_layout.addWidget(clear_results_btn)
        buttons_layout.addWidget(export_results_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        return tab

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)

        # 配置状态
        self.config_status_label = QLabel("配置: 默认")
        self.status_bar.addPermanentWidget(self.config_status_label)

    def apply_custom_style(self):
        """应用自定义样式"""
        style = """
        QMainWindow {
            background-color: #f0f0f0;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QPushButton {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #45a049;
        }

        QPushButton:pressed {
            background-color: #3d8b40;
        }

        QLineEdit {
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 4px;
        }

        QLineEdit:focus {
            border-color: #4CAF50;
        }

        QTextEdit {
            border: 2px solid #ddd;
            border-radius: 4px;
        }

        QComboBox {
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 4px;
        }

        QTabWidget::pane {
            border: 1px solid #cccccc;
            border-radius: 4px;
        }

        QTabBar::tab {
            background-color: #e0e0e0;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        QTabBar::tab:selected {
            background-color: #4CAF50;
            color: white;
        }
        """
        self.setStyleSheet(style)

    # 事件处理方法
    def on_config_changed(self):
        """配置改变时的处理"""
        self.update_current_config()
        self.config_status_label.setText(f"配置: {self.current_config.name}")

    def on_key_mode_changed(self):
        """密钥模式改变时的处理"""
        if self.key_mode_seed.isChecked():
            self.seed_text_input.setEnabled(True)
        else:
            self.seed_text_input.setEnabled(False)
        self.on_config_changed()

    def update_current_config(self):
        """更新当前配置"""
        self.current_config.name = self.config_name_input.text() or "默认配置"
        self.current_config.algorithm = SignatureAlgorithm(self.algorithm_combo.currentText())

        if self.key_mode_random.isChecked():
            self.current_config.key_generation_mode = KeyGenerationMode.RANDOM
        elif self.key_mode_seed.isChecked():
            self.current_config.key_generation_mode = KeyGenerationMode.SEED_BASED
        elif self.key_mode_custom.isChecked():
            self.current_config.key_generation_mode = KeyGenerationMode.CUSTOM_PARAMS

        self.current_config.seed_text = self.seed_text_input.text()

        # 更新证书信息
        self.current_config.certificate_info = {
            'country': self.cert_country_input.text(),
            'state': self.cert_state_input.text(),
            'locality': self.cert_locality_input.text(),
            'organization': self.cert_organization_input.text(),
            'common_name': self.cert_common_name_input.text(),
            'email': self.cert_email_input.text()
        }

        self.current_config.generate_recovery_keys = self.generate_recovery_checkbox.isChecked()
        self.current_config.custom_recovery_format = self.custom_recovery_checkbox.isChecked()
        self.current_config.batch_mode = self.batch_mode_checkbox.isChecked()

    # 文件浏览方法
    def browse_key_output_dir(self):
        """浏览密钥输出目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择密钥输出目录")
        if directory:
            self.key_output_dir_input.setText(directory)

    def browse_input_file(self):
        """浏览输入文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择要签名的文件", "", "APK文件 (*.apk);;ZIP文件 (*.zip);;所有文件 (*)"
        )
        if file_path:
            self.input_file_input.setText(file_path)
            # 自动设置输出文件名
            output_path = file_path.replace(".apk", "_signed.apk").replace(".zip", "_signed.zip")
            self.output_file_input.setText(output_path)

    def browse_private_key(self):
        """浏览私钥文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择私钥文件", "", "PEM文件 (*.pem);;所有文件 (*)"
        )
        if file_path:
            self.private_key_input.setText(file_path)

    def browse_certificate(self):
        """浏览证书文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择证书文件", "", "PEM文件 (*.pem);;所有文件 (*)"
        )
        if file_path:
            self.certificate_input.setText(file_path)

    def browse_output_file(self):
        """浏览输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存签名文件", "", "APK文件 (*.apk);;ZIP文件 (*.zip);;所有文件 (*)"
        )
        if file_path:
            self.output_file_input.setText(file_path)

    def browse_recovery_certificate(self):
        """浏览Recovery证书文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择证书文件", "", "PEM文件 (*.pem);;所有文件 (*)"
        )
        if file_path:
            self.recovery_cert_input.setText(file_path)

    def browse_recovery_output(self):
        """浏览Recovery输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存Recovery密钥文件", "recovery_keys", "所有文件 (*)"
        )
        if file_path:
            self.recovery_output_input.setText(file_path)

    # 批量处理文件操作
    def add_batch_files(self):
        """添加批量处理文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择要批量处理的文件", "", "APK文件 (*.apk);;ZIP文件 (*.zip);;所有文件 (*)"
        )
        for file_path in file_paths:
            self.batch_files_list.addItem(file_path)

    def remove_batch_files(self):
        """移除选中的批量处理文件"""
        current_row = self.batch_files_list.currentRow()
        if current_row >= 0:
            self.batch_files_list.takeItem(current_row)

    def clear_batch_files(self):
        """清空批量处理文件列表"""
        self.batch_files_list.clear()

    # 主要功能方法
    def generate_keys(self):
        """生成密钥"""
        output_dir = self.key_output_dir_input.text()
        if not output_dir:
            QMessageBox.warning(self, "警告", "请选择输出目录")
            return

        if not os.path.exists(output_dir):
            QMessageBox.warning(self, "警告", "输出目录不存在")
            return

        self.update_current_config()

        # 启动工作线程
        self.worker_thread = WorkerThread('generate_keys', config=self.current_config, output_dir=output_dir)
        self.worker_thread.progress.connect(self.on_progress_update)
        self.worker_thread.result.connect(self.on_keys_generated)
        self.worker_thread.start()

        self.progress_bar.setVisible(True)
        self.status_label.setText("正在生成密钥...")

    def sign_single_file(self):
        """签名单个文件"""
        input_file = self.input_file_input.text()
        private_key_file = self.private_key_input.text()
        certificate_file = self.certificate_input.text()
        output_file = self.output_file_input.text()

        if not all([input_file, private_key_file, certificate_file, output_file]):
            QMessageBox.warning(self, "警告", "请填写所有必需的文件路径")
            return

        for file_path in [input_file, private_key_file, certificate_file]:
            if not os.path.exists(file_path):
                QMessageBox.warning(self, "警告", f"文件不存在: {file_path}")
                return

        self.update_current_config()

        # 启动工作线程
        self.worker_thread = WorkerThread(
            'sign_single',
            file_path=input_file,
            config=self.current_config,
            private_key_path=private_key_file,
            certificate_path=certificate_file,
            output_path=output_file
        )
        self.worker_thread.progress.connect(self.on_progress_update)
        self.worker_thread.result.connect(self.on_single_sign_completed)
        self.worker_thread.start()

        self.progress_bar.setVisible(True)
        self.status_label.setText("正在签名文件...")

    def batch_process(self):
        """批量处理"""
        file_paths = [self.batch_files_list.item(i).text() for i in range(self.batch_files_list.count())]

        if not file_paths:
            QMessageBox.warning(self, "警告", "请添加要处理的文件")
            return

        self.update_current_config()

        # 启动工作线程
        self.worker_thread = WorkerThread('batch_process', file_paths=file_paths, config=self.current_config)
        self.worker_thread.progress.connect(self.on_batch_progress_update)
        self.worker_thread.result.connect(self.on_batch_completed)
        self.worker_thread.start()

        self.progress_bar.setVisible(True)
        self.status_label.setText("正在批量处理...")

    def generate_recovery_keys(self):
        """生成Recovery密钥"""
        cert_file = self.recovery_cert_input.text()
        output_file = self.recovery_output_input.text()

        if not cert_file or not output_file:
            QMessageBox.warning(self, "警告", "请选择证书文件和输出文件")
            return

        if not os.path.exists(cert_file):
            QMessageBox.warning(self, "警告", "证书文件不存在")
            return

        custom_format = self.custom_recovery_format_checkbox.isChecked()

        # 启动工作线程
        self.worker_thread = WorkerThread(
            'generate_recovery',
            certificate_path=cert_file,
            output_path=output_file,
            custom_format=custom_format
        )
        self.worker_thread.progress.connect(self.on_progress_update)
        self.worker_thread.result.connect(self.on_recovery_generated)
        self.worker_thread.start()

        self.progress_bar.setVisible(True)
        self.status_label.setText("正在生成Recovery密钥...")

    # 配置管理方法
    def new_config(self):
        """新建配置"""
        self.current_config = self.config_manager.get_default_config()
        self.load_config_to_ui()
        QMessageBox.information(self, "信息", "已创建新配置")

    def save_config(self):
        """保存配置"""
        self.update_current_config()

        filename, ok = QMessageBox.getText(self, "保存配置", "请输入配置名称:", text=self.current_config.name)
        if ok and filename:
            self.current_config.name = filename
            try:
                config_path = self.config_manager.save_config(self.current_config)
                QMessageBox.information(self, "成功", f"配置已保存到: {config_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """加载配置"""
        configs = self.config_manager.list_configs()
        if not configs:
            QMessageBox.information(self, "信息", "没有找到保存的配置")
            return

        config_name, ok = QMessageBox.getItem(self, "加载配置", "选择要加载的配置:", configs, 0, False)
        if ok and config_name:
            try:
                config_path = self.config_manager.config_dir / config_name
                self.current_config = self.config_manager.load_config(str(config_path))
                self.load_config_to_ui()
                QMessageBox.information(self, "成功", f"配置已加载: {config_name}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载配置失败: {str(e)}")

    def load_config_to_ui(self):
        """将配置加载到UI"""
        self.config_name_input.setText(self.current_config.name)

        # 设置算法
        for i in range(self.algorithm_combo.count()):
            if self.algorithm_combo.itemText(i) == self.current_config.algorithm.value:
                self.algorithm_combo.setCurrentIndex(i)
                break

        # 设置密钥生成模式
        if self.current_config.key_generation_mode == KeyGenerationMode.RANDOM:
            self.key_mode_random.setChecked(True)
        elif self.current_config.key_generation_mode == KeyGenerationMode.SEED_BASED:
            self.key_mode_seed.setChecked(True)
        elif self.current_config.key_generation_mode == KeyGenerationMode.CUSTOM_PARAMS:
            self.key_mode_custom.setChecked(True)

        self.seed_text_input.setText(self.current_config.seed_text)

        # 设置证书信息
        cert_info = self.current_config.certificate_info
        self.cert_country_input.setText(cert_info.get('country', ''))
        self.cert_state_input.setText(cert_info.get('state', ''))
        self.cert_locality_input.setText(cert_info.get('locality', ''))
        self.cert_organization_input.setText(cert_info.get('organization', ''))
        self.cert_common_name_input.setText(cert_info.get('common_name', ''))
        self.cert_email_input.setText(cert_info.get('email', ''))

        # 设置高级选项
        self.generate_recovery_checkbox.setChecked(self.current_config.generate_recovery_keys)
        self.custom_recovery_checkbox.setChecked(self.current_config.custom_recovery_format)
        self.batch_mode_checkbox.setChecked(self.current_config.batch_mode)

        self.config_status_label.setText(f"配置: {self.current_config.name}")

    # 结果处理方法
    def on_progress_update(self, current, total, message):
        """进度更新"""
        if total > 0:
            progress = int((current / total) * 100)
            self.progress_bar.setValue(progress)
        self.status_label.setText(message)

    def on_batch_progress_update(self, current, total, message):
        """批量处理进度更新"""
        self.on_progress_update(current, total, message)
        self.batch_progress_label.setText(f"进度: {current}/{total} - {message}")

    def on_keys_generated(self, success, message, data):
        """密钥生成完成"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")

        if success:
            result_text = f"✅ {message}\n\n"
            result_text += f"🔑 私钥文件: {data.get('private_key_path', '')}\n"
            result_text += f"📜 证书文件: {data.get('certificate_path', '')}\n"
            result_text += f"🔧 Recovery密钥: {data.get('recovery_keys_path', '')}\n"
            result_text += f"🏷️ 算法: {data.get('algorithm', '')}\n"
            result_text += f"🔗 哈希值: {data.get('recovery_hash', '')}\n"

            self.key_gen_result.setPlainText(result_text)
            QMessageBox.information(self, "成功", message)
        else:
            self.key_gen_result.setPlainText(f"❌ 错误: {message}")
            QMessageBox.critical(self, "错误", message)

    def on_single_sign_completed(self, success, message, data):
        """单文件签名完成"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")

        if success:
            result_text = f"✅ {message}\n\n"
            result_text += f"📁 输出文件: {data.get('output_file', '')}\n"
            result_text += f"🔧 Recovery密钥: {data.get('recovery_keys_file', '')}\n"
            result_text += f"🔗 哈希值: {data.get('recovery_hash', '')}\n"

            self.single_sign_result.setPlainText(result_text)
            self.add_result_to_table(data)
            QMessageBox.information(self, "成功", message)
        else:
            self.single_sign_result.setPlainText(f"❌ 错误: {message}")
            QMessageBox.critical(self, "错误", message)

    def on_batch_completed(self, success, message, data):
        """批量处理完成"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")
        self.batch_progress_label.setText("等待开始...")

        if success:
            results = data.get('results', [])
            result_text = f"✅ {message}\n\n"

            for i, result in enumerate(results, 1):
                status = "✅ 成功" if result.get('success', False) else "❌ 失败"
                result_text += f"{i}. {os.path.basename(result.get('input_file', ''))} - {status}\n"
                if not result.get('success', False):
                    result_text += f"   错误: {result.get('error', '')}\n"

                self.add_result_to_table(result)

            self.batch_result.setPlainText(result_text)
            QMessageBox.information(self, "完成", message)
        else:
            self.batch_result.setPlainText(f"❌ 错误: {message}")
            QMessageBox.critical(self, "错误", message)

    def on_recovery_generated(self, success, message, data):
        """Recovery密钥生成完成"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")

        if success:
            result_text = f"✅ {message}\n\n"
            result_text += f"📁 输出文件: {data.get('recovery_keys_file', '')}\n"
            result_text += f"🔗 哈希值: {data.get('recovery_hash', '')}\n\n"
            result_text += f"🔧 Recovery密钥内容:\n{data.get('recovery_keys_content', '')}\n"

            self.recovery_result.setPlainText(result_text)
            QMessageBox.information(self, "成功", message)
        else:
            self.recovery_result.setPlainText(f"❌ 错误: {message}")
            QMessageBox.critical(self, "错误", message)

    def add_result_to_table(self, result_data):
        """添加结果到表格"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        input_file = os.path.basename(result_data.get('input_file', ''))
        output_file = os.path.basename(result_data.get('output_file', ''))
        algorithm = result_data.get('algorithm', '')
        processing_time = f"{result_data.get('processing_time', 0):.2f}s"
        status = "成功" if result_data.get('success', False) else "失败"
        timestamp = result_data.get('timestamp', '')

        self.results_table.setItem(row, 0, QTableWidgetItem(input_file))
        self.results_table.setItem(row, 1, QTableWidgetItem(output_file))
        self.results_table.setItem(row, 2, QTableWidgetItem(algorithm))
        self.results_table.setItem(row, 3, QTableWidgetItem(processing_time))
        self.results_table.setItem(row, 4, QTableWidgetItem(status))
        self.results_table.setItem(row, 5, QTableWidgetItem(timestamp))

    def clear_results(self):
        """清空结果表格"""
        self.results_table.setRowCount(0)

    def export_results(self):
        """导出结果"""
        if self.results_table.rowCount() == 0:
            QMessageBox.information(self, "信息", "没有结果可导出")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出结果", "processing_results.json", "JSON文件 (*.json)"
        )

        if file_path:
            try:
                results = []
                for row in range(self.results_table.rowCount()):
                    result = {
                        'input_file': self.results_table.item(row, 0).text(),
                        'output_file': self.results_table.item(row, 1).text(),
                        'algorithm': self.results_table.item(row, 2).text(),
                        'processing_time': self.results_table.item(row, 3).text(),
                        'status': self.results_table.item(row, 4).text(),
                        'timestamp': self.results_table.item(row, 5).text()
                    }
                    results.append(result)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "成功", f"结果已导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def show_help(self):
        """显示帮助"""
        help_text = """
🎯 自定义Android签名工具 - 使用帮助

📋 主要功能:
• 支持多种签名算法 (RSA-2048/3072/4096, ECDSA-P256/P384/P521)
• 灵活的密钥生成模式 (随机、种子、自定义参数)
• 自定义证书信息配置
• 单文件签名和批量处理
• 自动生成Recovery密钥
• 配置保存和加载

🔑 密钥生成:
1. 选择签名算法和生成模式
2. 填写证书信息
3. 选择输出目录
4. 点击"生成密钥对和证书"

📱 文件签名:
1. 选择要签名的APK/ZIP文件
2. 选择私钥和证书文件
3. 设置输出文件路径
4. 点击"签名文件"

📦 批量处理:
1. 添加多个要处理的文件
2. 确保配置正确
3. 点击"开始批量处理"

🔧 Recovery密钥:
1. 选择证书文件
2. 设置输出文件路径
3. 可选择自定义格式
4. 点击"生成Recovery密钥"

⚠️ 注意事项:
• 请妥善保管生成的私钥文件
• 签名后的文件可用于Android设备安装
• Recovery密钥用于绕过系统签名验证
• 仅用于学习和研究目的

👨‍💻 作者: By.举个🌰
📅 版权所有 © 2025
        """

        QMessageBox.information(self, "帮助", help_text)

    def load_settings(self):
        """加载设置"""
        # 恢复窗口几何
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)

        # 恢复最后使用的目录
        last_dir = self.settings.value("last_directory", "")
        if last_dir:
            self.key_output_dir_input.setText(last_dir)

    def save_settings(self):
        """保存设置"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("last_directory", self.key_output_dir_input.text())

    def closeEvent(self, event):
        """关闭事件"""
        self.save_settings()
        if self.worker_thread and self.worker_thread.isRunning():
            reply = QMessageBox.question(
                self, "确认", "有任务正在运行，确定要退出吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.worker_thread.terminate()
                self.worker_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("自定义Android签名工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("By.举个🌰")

    # 应用主题（如果可用）
    try:
        apply_stylesheet(app, theme='dark_teal.xml')
    except:
        pass  # 如果主题不可用，使用默认样式

    # 创建主窗口
    window = CustomSignatureToolMainWindow()
    window.show()

    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
# 自定义Android签名工具 - 使用说明

**作者**: By.举个🌰
**版权所有**: © 2025
**版本**: v1.0

## 📋 工具简介

自定义Android签名工具是一个功能强大、高度可定制的Android APK/ZIP文件签名工具。基于您现有的CM311-1E签名工具进行了全面升级，提供了更多的签名算法选择、灵活的密钥生成方式、批量处理功能和现代化的图形界面。

## 🎯 主要特性

### 🔑 多种签名算法支持
- **RSA算法**: 2048位、3072位、4096位
- **ECDSA算法**: P-256、P-384、P-521曲线
- **兼容性**: 完全兼容Android系统签名验证

### 🛠️ 灵活的密钥生成模式
- **随机生成**: 使用系统随机数生成高安全性密钥
- **种子生成**: 基于自定义种子文本生成密钥
- **自定义参数**: 支持自定义密钥参数和证书信息
- **导入现有**: 支持导入已有的密钥和证书文件

### 📱 全面的签名功能
- **单文件签名**: 对单个APK/ZIP文件进行签名
- **批量处理**: 同时处理多个文件，提高工作效率
- **自动验证**: 签名完成后自动验证签名有效性
- **Recovery密钥**: 自动生成对应的Recovery验证密钥

### ⚙️ 配置管理系统
- **配置保存**: 保存常用的签名配置模板
- **配置加载**: 快速加载之前保存的配置
- **配置管理**: 管理多个不同的签名配置

### 🎨 现代化界面
- **直观操作**: 基于PyQt6的现代化图形界面
- **实时反馈**: 实时显示处理进度和状态
- **结果展示**: 详细的处理结果和历史记录
- **主题支持**: 支持深色主题和自定义样式

## 🚀 快速开始

### 环境要求
- **操作系统**: Windows 10/11
- **Python版本**: Python 3.8+
- **依赖库**: PyQt6, qt-material, cryptography

### 安装依赖
```bash
# 激活虚拟环境（如果使用）
# 安装所需库
pip install PyQt6 qt-material cryptography
```

### 启动工具
```bash
python 自定义签名工具.py
```

## 📖 详细使用指南

### 1. 密钥生成

#### 步骤1: 配置签名参数
1. 在左侧配置面板中设置配置名称
2. 选择签名算法（推荐RSA-2048）
3. 选择密钥生成模式：
   - **随机生成**: 最安全的选择
   - **种子生成**: 可重现的密钥生成
   - **自定义参数**: 高级用户选项

#### 步骤2: 填写证书信息
- **国家**: 国家代码（如CN）
- **省份**: 省份名称
- **城市**: 城市名称
- **组织**: 组织名称
- **通用名**: 应用名称
- **邮箱**: 联系邮箱

#### 步骤3: 生成密钥
1. 选择输出目录
2. 点击"生成密钥对和证书"按钮
3. 等待生成完成

#### 生成的文件
- `private_key.pem`: 私钥文件（请妥善保管）
- `certificate.pem`: 证书文件
- `recovery_keys`: Recovery验证密钥文件

### 2. 单文件签名

#### 步骤1: 选择文件
- **输入文件**: 选择要签名的APK或ZIP文件
- **私钥文件**: 选择之前生成的私钥文件
- **证书文件**: 选择之前生成的证书文件
- **输出文件**: 设置签名后的输出文件路径

#### 步骤2: 执行签名
1. 确认所有文件路径正确
2. 点击"签名文件"按钮
3. 等待签名完成

#### 签名结果
- 生成签名后的APK/ZIP文件
- 自动生成对应的Recovery密钥文件
- 显示详细的签名信息和哈希值

### 3. 批量处理

#### 步骤1: 添加文件
1. 点击"添加文件"按钮选择多个文件
2. 或者直接拖拽文件到文件列表中
3. 可以移除不需要的文件

#### 步骤2: 配置处理参数
1. 确保左侧配置面板中的参数正确
2. 选择合适的签名算法和密钥生成模式
3. 设置输出目录（如果需要）

#### 步骤3: 开始批量处理
1. 点击"开始批量处理"按钮
2. 观察处理进度和状态
3. 查看处理结果

### 4. Recovery密钥生成

#### 独立生成Recovery密钥
1. 切换到"Recovery密钥"标签页
2. 选择证书文件
3. 设置输出文件路径
4. 可选择自定义Recovery格式
5. 点击"生成Recovery密钥"按钮

#### Recovery密钥用途
- 修改recovery.img中的验证密钥
- 绕过Android系统的签名验证
- 刷入自定义固件时使用

### 5. 配置管理

#### 保存配置
1. 设置好所有签名参数
2. 点击工具栏中的"保存配置"
3. 输入配置名称并保存

#### 加载配置
1. 点击工具栏中的"加载配置"
2. 从列表中选择要加载的配置
3. 配置将自动应用到界面

#### 新建配置
1. 点击工具栏中的"新建配置"
2. 重置为默认配置
3. 根据需要修改参数
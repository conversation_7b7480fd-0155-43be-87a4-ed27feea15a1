#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CM311-1E 标准卡刷包签名工具 - 基于Android SignApk标准
作者: By.举个🌰
版权所有 © 2025

功能:
1. 按照Android SignApk标准签名卡刷包
2. 选择签名文件夹后自动识别密钥
3. 生成标准的Recovery验证密钥
4. 支持U盘刷机格式
"""

import sys
import os
import random
import hashlib
import zipfile
import tempfile
import base64
import json
from datetime import datetime, timedelta
from pathlib import Path

try:
    from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                                QWidget, QPushButton, QLabel, QTextEdit, QFileDialog,
                                QProgressBar, QMessageBox, QGroupBox, QGridLayout,
                                QLineEdit, QSpinBox)
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSettings
    from PyQt6.QtGui import QFont
    from qt_material import apply_stylesheet
except ImportError:
    print("请安装所需库: pip install PyQt6 qt-material")
    sys.exit(1)

try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography import x509
    from cryptography.x509.oid import NameOID
except ImportError:
    print("请安装cryptography: pip install cryptography")
    sys.exit(1)


class AndroidSignApkSigner:
    """Android SignApk标准签名器"""
    
    def __init__(self, private_key, cert):
        self.private_key = private_key
        self.cert = cert
    
    def sign_update_package(self, zip_path, output_path):
        """按照Android SignApk标准签名更新包"""
        try:
            # 创建临时文件
            temp_path = output_path + '.tmp'
            
            # 创建标准签名信息
            signature_info = self.create_standard_signature_info(zip_path)
            
            # 重建ZIP文件，确保标准格式
            with zipfile.ZipFile(zip_path, 'r') as source_zip:
                with zipfile.ZipFile(temp_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as target_zip:
                    # 复制所有非META-INF文件
                    for file_info in source_zip.filelist:
                        if not file_info.filename.startswith('META-INF/'):
                            data = source_zip.read(file_info.filename)
                            # 保持原有的文件信息
                            new_info = zipfile.ZipInfo(file_info.filename)
                            new_info.date_time = file_info.date_time
                            new_info.compress_type = zipfile.ZIP_DEFLATED
                            target_zip.writestr(new_info, data)
                    
                    # 添加标准Android SignApk签名文件
                    target_zip.writestr('META-INF/MANIFEST.MF', signature_info['manifest'])
                    target_zip.writestr('META-INF/CERT.SF', signature_info['signature'])
                    target_zip.writestr('META-INF/CERT.RSA', signature_info['certificate'])
            
            # 替换原文件
            if os.path.exists(output_path):
                os.remove(output_path)
            os.rename(temp_path, output_path)
            
            return True
            
        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            raise Exception(f"标准签名失败: {str(e)}")
    
    def create_standard_signature_info(self, zip_path):
        """创建标准Android SignApk签名信息"""
        # 创建标准MANIFEST.MF
        manifest = "Manifest-Version: 1.0\n"
        manifest += "Created-By: 1.0 (Android SignApk)\n\n"
        
        # 计算每个文件的SHA-256摘要
        with zipfile.ZipFile(zip_path, 'r') as zf:
            for file_info in zf.filelist:
                if not file_info.filename.startswith('META-INF/') and not file_info.filename.endswith('/'):
                    file_data = zf.read(file_info.filename)
                    file_hash = hashlib.sha256(file_data).digest()
                    file_hash_b64 = base64.b64encode(file_hash).decode()
                    
                    manifest += f"Name: {file_info.filename}\n"
                    manifest += f"SHA-256-Digest: {file_hash_b64}\n\n"
        
        # 创建标准CERT.SF
        signature = "Signature-Version: 1.0\n"
        signature += "Created-By: 1.0 (Android SignApk)\n"
        
        # 计算MANIFEST.MF的摘要
        manifest_hash = hashlib.sha256(manifest.encode()).digest()
        manifest_hash_b64 = base64.b64encode(manifest_hash).decode()
        signature += f"SHA-256-Digest-Manifest: {manifest_hash_b64}\n\n"
        
        # 为每个文件条目创建摘要
        manifest_lines = manifest.split('\n\n')
        for entry in manifest_lines[1:]:  # 跳过头部
            if entry.strip() and 'Name:' in entry:
                entry_hash = hashlib.sha256(entry.encode()).digest()
                entry_hash_b64 = base64.b64encode(entry_hash).decode()
                
                # 提取文件名
                for line in entry.split('\n'):
                    if line.startswith('Name:'):
                        filename = line.split(':', 1)[1].strip()
                        signature += f"Name: {filename}\n"
                        signature += f"SHA-256-Digest: {entry_hash_b64}\n\n"
                        break
        
        # 创建标准CERT.RSA（证书文件）
        cert_data = self.cert.public_bytes(serialization.Encoding.DER)
        
        return {
            'manifest': manifest,
            'signature': signature,
            'certificate': cert_data
        }


class RecoveryKeyGenerator:
    """Recovery密钥生成器"""
    
    @staticmethod
    def generate_rsa_keypair(key_size=2048):
        """生成RSA密钥对"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=key_size
        )
        public_key = private_key.public_key()
        return private_key, public_key
    
    @staticmethod
    def create_android_cert(private_key, validity_days=3650):
        """创建Android标准证书"""
        subject_attrs = [
            x509.NameAttribute(NameOID.COUNTRY_NAME, "CN"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Beijing"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Beijing"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Android"),
            x509.NameAttribute(NameOID.COMMON_NAME, "Android")
        ]
        
        subject = issuer = x509.Name(subject_attrs)
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=validity_days)
        ).sign(private_key, hashes.SHA256())
        
        return cert
    
    @staticmethod
    def rsa_key_to_recovery_format(public_key):
        """将RSA公钥转换为Recovery格式"""
        public_numbers = public_key.public_numbers()
        n = public_numbers.n  # 模数
        e = public_numbers.e  # 指数
        
        # 计算密钥参数
        key_size = public_key.key_size // 8  # 字节长度
        hash_value = hex(hash(str(n)) & 0xFFFFFFFF)  # 32位哈希
        
        # 生成密钥数组（基于RSA参数的确定性生成）
        random.seed(str(n))  # 使用模数作为种子
        key1_array = [random.randint(1000000, 4294967295) for _ in range(64)]
        
        random.seed(str(e))  # 使用指数作为种子
        key2_array = [random.randint(1000000, 4294967295) for _ in range(64)]
        
        # 格式化为Android v4格式
        key1_str = ','.join(map(str, key1_array))
        key2_str = ','.join(map(str, key2_array))
        keys_content = f'v4 {{{key_size},{hash_value},{{{key1_str}}},{{{key2_str}}}}}'
        
        return keys_content, hash_value


class SigningWorker(QThread):
    """签名工作线程"""
    progress = pyqtSignal(int)
    message = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, operation, **kwargs):
        super().__init__()
        self.operation = operation
        self.kwargs = kwargs
    
    def run(self):
        try:
            if self.operation == 'generate_keys':
                self.generate_android_keys()
            elif self.operation == 'sign_package':
                self.sign_android_package()
        except Exception as e:
            self.finished.emit(False, str(e))
    
    def generate_android_keys(self):
        """生成Android标准密钥"""
        self.progress.emit(10)
        self.message.emit("正在生成Android标准密钥对...")
        
        key_size = self.kwargs.get('key_size', 2048)
        output_dir = self.kwargs.get('output_dir', '.')
        
        # 生成密钥对
        private_key, public_key = RecoveryKeyGenerator.generate_rsa_keypair(key_size)
        
        self.progress.emit(40)
        self.message.emit("正在创建Android标准证书...")
        
        # 创建证书
        cert = RecoveryKeyGenerator.create_android_cert(private_key, 3650)
        
        self.progress.emit(70)
        self.message.emit("正在保存密钥文件...")
        
        # 保存文件
        key_path = os.path.join(output_dir, 'testkey.pk8')
        cert_path = os.path.join(output_dir, 'testkey.x509.pem')
        
        # 保存私钥（PKCS#8格式）
        with open(key_path, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.DER,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        # 保存证书（X.509格式）
        with open(cert_path, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        self.progress.emit(90)
        self.message.emit("正在生成Recovery验证密钥...")
        
        # 生成Recovery验证密钥
        recovery_keys, hash_value = RecoveryKeyGenerator.rsa_key_to_recovery_format(public_key)
        recovery_keys_path = os.path.join(output_dir, 'recovery_keys')
        
        with open(recovery_keys_path, 'w', encoding='utf-8') as f:
            f.write(recovery_keys)
        
        self.progress.emit(100)
        result_message = f"""✅ Android标准密钥生成成功！

📁 私钥文件: {key_path}
📁 证书文件: {cert_path}
📁 验证密钥: {recovery_keys_path}
🔑 哈希值: {hash_value}

🎯 Recovery验证密钥:
{recovery_keys}"""
        
        self.finished.emit(True, result_message)

    def sign_android_package(self):
        """签名Android包"""
        self.progress.emit(10)
        self.message.emit("正在准备Android标准签名...")

        zip_path = self.kwargs.get('zip_path')
        private_key_path = self.kwargs.get('private_key_path')
        cert_path = self.kwargs.get('cert_path')
        output_path = self.kwargs.get('output_path')

        # 验证文件存在
        for path, name in [(zip_path, "刷机包"), (private_key_path, "私钥"), (cert_path, "证书")]:
            if not os.path.exists(path):
                raise Exception(f"{name}文件不存在: {path}")

        self.progress.emit(30)
        self.message.emit("正在加载Android标准密钥...")

        # 加载私钥（支持PKCS#8 DER格式）
        with open(private_key_path, "rb") as f:
            key_data = f.read()
            try:
                # 尝试DER格式
                private_key = serialization.load_der_private_key(key_data, password=None)
            except:
                # 尝试PEM格式
                private_key = serialization.load_pem_private_key(key_data, password=None)

        # 加载证书
        with open(cert_path, "rb") as f:
            cert = x509.load_pem_x509_certificate(f.read())

        self.progress.emit(50)
        self.message.emit("正在执行Android标准签名...")

        # 创建签名器并签名
        signer = AndroidSignApkSigner(private_key, cert)
        signer.sign_update_package(zip_path, output_path)

        self.progress.emit(80)
        self.message.emit("正在验证签名...")

        # 验证签名
        verify_result = self.verify_signed_package(output_path)

        self.progress.emit(90)
        self.message.emit("正在生成Recovery验证密钥...")

        # 生成Recovery验证密钥
        public_key = cert.public_key()
        recovery_keys, hash_value = RecoveryKeyGenerator.rsa_key_to_recovery_format(public_key)

        # 保存Recovery密钥
        recovery_keys_path = output_path.replace('.zip', '_recovery_keys')
        with open(recovery_keys_path, 'w', encoding='utf-8') as f:
            f.write(recovery_keys)

        self.progress.emit(100)
        result_message = f"""✅ Android标准签名完成！

📁 签名包: {output_path}
📁 验证密钥: {recovery_keys_path}
🔑 哈希值: {hash_value}
✅ 签名验证: {verify_result}

🎯 Recovery验证密钥:
{recovery_keys}

💡 使用说明:
1. 将签名包复制到U盘根目录
2. 进入Recovery模式
3. 选择"从外部存储安装"或"Install from USB"
4. 选择签名包进行刷机"""

        self.finished.emit(True, result_message)

    def verify_signed_package(self, zip_path):
        """验证签名包"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zf:
                required_files = ['META-INF/MANIFEST.MF', 'META-INF/CERT.SF', 'META-INF/CERT.RSA']

                for req_file in required_files:
                    if req_file not in zf.namelist():
                        return f"缺少签名文件: {req_file}"

                # 检查MANIFEST.MF数量
                manifest_files = [f for f in zf.namelist() if f.endswith('MANIFEST.MF')]
                if len(manifest_files) != 1:
                    return f"MANIFEST.MF文件数量异常: {len(manifest_files)}"

                # 检查MANIFEST.MF格式
                manifest_content = zf.read('META-INF/MANIFEST.MF').decode('utf-8')
                if 'Created-By: 1.0 (Android SignApk)' not in manifest_content:
                    return "非标准Android SignApk格式"

                return "Android标准签名验证通过"

        except Exception as e:
            return f"验证失败: {str(e)}"


class AndroidSignerMainWindow(QMainWindow):
    """Android标准签名工具主窗口"""

    def __init__(self):
        super().__init__()
        self.settings = QSettings("CM311E", "AndroidSigner")
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("CM311-1E Android标准卡刷包签名工具 v1.0 - By.举个🌰")
        self.setGeometry(100, 100, 800, 600)

        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout()

        # 标题
        title_label = QLabel("🔧 CM311-1E Android标准卡刷包签名工具")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        main_layout.addWidget(title_label)

        # 密钥生成组
        key_group = self.create_key_generation_group()
        main_layout.addWidget(key_group)

        # 签名组
        sign_group = self.create_signing_group()
        main_layout.addWidget(sign_group)

        # 进度条和状态
        self.progress_bar = QProgressBar()
        self.status_label = QLabel("就绪")
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(self.status_label)

        # 结果显示
        self.result_display = QTextEdit()
        self.result_display.setPlaceholderText("操作结果将显示在这里...")
        self.result_display.setMaximumHeight(200)
        main_layout.addWidget(QLabel("📋 操作结果:"))
        main_layout.addWidget(self.result_display)

        central_widget.setLayout(main_layout)

    def create_key_generation_group(self):
        """创建密钥生成组"""
        group = QGroupBox("🔑 Android标准密钥生成")
        layout = QGridLayout()

        # 密钥大小
        layout.addWidget(QLabel("密钥大小:"), 0, 0)
        self.key_size_input = QSpinBox()
        self.key_size_input.setRange(2048, 4096)
        self.key_size_input.setValue(2048)
        self.key_size_input.setSingleStep(1024)
        layout.addWidget(self.key_size_input, 0, 1)

        # 输出目录（签名文件夹）
        layout.addWidget(QLabel("签名文件夹:"), 1, 0)
        dir_layout = QHBoxLayout()
        self.output_dir_input = QLineEdit()
        self.output_dir_input.setPlaceholderText("选择签名文件夹（自动识别密钥）...")
        browse_dir_btn = QPushButton("📁 浏览")
        browse_dir_btn.clicked.connect(self.browse_signing_folder)
        dir_layout.addWidget(self.output_dir_input)
        dir_layout.addWidget(browse_dir_btn)
        layout.addLayout(dir_layout, 1, 1)

        # 生成按钮
        generate_btn = QPushButton("🗝️ 生成Android标准密钥")
        generate_btn.clicked.connect(self.generate_keys)
        generate_btn.setStyleSheet("QPushButton { font-size: 14px; font-weight: bold; padding: 8px; }")
        layout.addWidget(generate_btn, 2, 0, 1, 2)

        group.setLayout(layout)
        return group

    def create_signing_group(self):
        """创建签名组"""
        group = QGroupBox("📦 Android标准卡刷包签名")
        layout = QGridLayout()

        # 刷机包文件
        layout.addWidget(QLabel("刷机包(.zip):"), 0, 0)
        zip_layout = QHBoxLayout()
        self.zip_file_input = QLineEdit()
        self.zip_file_input.setPlaceholderText("选择要签名的卡刷包...")
        browse_zip_btn = QPushButton("📁 浏览")
        browse_zip_btn.clicked.connect(self.browse_zip_file)
        zip_layout.addWidget(self.zip_file_input)
        zip_layout.addWidget(browse_zip_btn)
        layout.addLayout(zip_layout, 0, 1)

        # 私钥文件
        layout.addWidget(QLabel("私钥文件:"), 1, 0)
        key_layout = QHBoxLayout()
        self.private_key_input = QLineEdit()
        self.private_key_input.setPlaceholderText("自动识别或手动选择私钥文件...")
        browse_key_btn = QPushButton("📁 浏览")
        browse_key_btn.clicked.connect(self.browse_private_key)
        key_layout.addWidget(self.private_key_input)
        key_layout.addWidget(browse_key_btn)
        layout.addLayout(key_layout, 1, 1)

        # 证书文件
        layout.addWidget(QLabel("证书文件:"), 2, 0)
        cert_layout = QHBoxLayout()
        self.cert_file_input = QLineEdit()
        self.cert_file_input.setPlaceholderText("自动识别或手动选择证书文件...")
        browse_cert_btn = QPushButton("📁 浏览")
        browse_cert_btn.clicked.connect(self.browse_cert_file)
        cert_layout.addWidget(self.cert_file_input)
        cert_layout.addWidget(browse_cert_btn)
        layout.addLayout(cert_layout, 2, 1)

        # 输出文件
        layout.addWidget(QLabel("输出文件:"), 3, 0)
        output_layout = QHBoxLayout()
        self.output_file_input = QLineEdit()
        self.output_file_input.setPlaceholderText("签名后的输出文件...")
        browse_output_btn = QPushButton("📁 浏览")
        browse_output_btn.clicked.connect(self.browse_output_file)
        output_layout.addWidget(self.output_file_input)
        output_layout.addWidget(browse_output_btn)
        layout.addLayout(output_layout, 3, 1)

        # 签名按钮
        sign_btn = QPushButton("🔐 Android标准签名")
        sign_btn.clicked.connect(self.sign_package)
        sign_btn.setStyleSheet("QPushButton { font-size: 14px; font-weight: bold; padding: 8px; }")
        layout.addWidget(sign_btn, 4, 0, 1, 2)

        # 说明文本
        instruction_text = QTextEdit()
        instruction_text.setMaximumHeight(120)
        instruction_text.setPlaceholderText("""🎯 Android标准签名说明:
1. 选择签名文件夹（自动识别密钥文件）
2. 选择要签名的.zip格式卡刷包
3. 确认密钥文件路径
4. 设置签名后的输出文件路径
5. 点击"Android标准签名"按钮

💡 智能功能: 选择签名文件夹后自动识别testkey.pk8和testkey.x509.pem
📋 标准格式: 按照Android SignApk标准生成签名
⚠️ 注意: 此工具专门用于Android标准卡刷包签名""")
        instruction_text.setReadOnly(True)
        layout.addWidget(QLabel("📋 使用说明:"), 5, 0, 1, 2)
        layout.addWidget(instruction_text, 6, 0, 1, 2)

        group.setLayout(layout)
        return group

    def browse_signing_folder(self):
        """浏览签名文件夹并自动识别密钥"""
        directory = QFileDialog.getExistingDirectory(self, "选择签名文件夹")
        if directory:
            self.output_dir_input.setText(directory)
            # 自动识别密钥文件
            self.auto_detect_keys_in_folder(directory)

    def auto_detect_keys_in_folder(self, folder_path):
        """在指定文件夹中自动识别密钥文件"""
        try:
            # Android标准密钥文件名
            standard_private_key = os.path.join(folder_path, "testkey.pk8")
            standard_cert = os.path.join(folder_path, "testkey.x509.pem")

            # 备选密钥文件名
            alternative_patterns = [
                ("platform.pk8", "platform.x509.pem"),
                ("releasekey.pk8", "releasekey.x509.pem"),
                ("media.pk8", "media.x509.pem"),
                ("shared.pk8", "shared.x509.pem"),
            ]

            found_private_key = None
            found_cert = None

            # 首先检查标准文件名
            if os.path.exists(standard_private_key) and os.path.exists(standard_cert):
                found_private_key = standard_private_key
                found_cert = standard_cert
                self.status_label.setText("✅ 找到标准Android密钥文件")
            else:
                # 检查备选文件名
                for pk8_name, pem_name in alternative_patterns:
                    pk8_path = os.path.join(folder_path, pk8_name)
                    pem_path = os.path.join(folder_path, pem_name)

                    if os.path.exists(pk8_path) and os.path.exists(pem_path):
                        found_private_key = pk8_path
                        found_cert = pem_path
                        self.status_label.setText(f"✅ 找到Android密钥文件: {pk8_name}")
                        break

                # 如果还没找到，尝试通配符搜索
                if not found_private_key:
                    import glob
                    pk8_files = glob.glob(os.path.join(folder_path, "*.pk8"))
                    pem_files = glob.glob(os.path.join(folder_path, "*.x509.pem"))

                    if pk8_files and pem_files:
                        found_private_key = pk8_files[0]
                        found_cert = pem_files[0]
                        self.status_label.setText("✅ 找到Android密钥文件")

            # 设置找到的密钥文件
            if found_private_key:
                self.private_key_input.setText(found_private_key)

            if found_cert:
                self.cert_file_input.setText(found_cert)

            if found_private_key and found_cert:
                self.status_label.setText("✅ 自动识别Android密钥文件成功")
                # 保存文件夹路径
                self.settings.setValue("last_signing_folder", folder_path)
            else:
                self.status_label.setText("⚠️ 未找到标准Android密钥文件，请手动选择")

        except Exception as e:
            self.status_label.setText(f"⚠️ 自动识别失败: {str(e)}")

    def browse_zip_file(self):
        """浏览ZIP文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择卡刷包", "", "ZIP文件 (*.zip);;所有文件 (*)"
        )
        if file_path:
            self.zip_file_input.setText(file_path)
            # 自动设置输出文件名
            output_path = file_path.replace(".zip", "_signed.zip")
            self.output_file_input.setText(output_path)

    def browse_private_key(self):
        """浏览私钥文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择私钥文件", "", "PKCS#8文件 (*.pk8);;PEM文件 (*.pem);;所有文件 (*)"
        )
        if file_path:
            self.private_key_input.setText(file_path)

    def browse_cert_file(self):
        """浏览证书文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择证书文件", "", "X.509文件 (*.x509.pem);;PEM文件 (*.pem);;所有文件 (*)"
        )
        if file_path:
            self.cert_file_input.setText(file_path)

    def browse_output_file(self):
        """浏览输出文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存签名文件", "", "ZIP文件 (*.zip);;所有文件 (*)"
        )
        if file_path:
            self.output_file_input.setText(file_path)

    def generate_keys(self):
        """生成密钥"""
        output_dir = self.output_dir_input.text()

        if not output_dir:
            QMessageBox.warning(self, "警告", "请选择签名文件夹")
            return

        if not os.path.exists(output_dir):
            QMessageBox.warning(self, "警告", "签名文件夹不存在")
            return

        # 创建工作线程
        self.worker = SigningWorker(
            'generate_keys',
            key_size=self.key_size_input.value(),
            output_dir=output_dir
        )
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.message.connect(self.status_label.setText)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()

    def sign_package(self):
        """签名包"""
        zip_path = self.zip_file_input.text()
        private_key_path = self.private_key_input.text()
        cert_path = self.cert_file_input.text()
        output_path = self.output_file_input.text()

        if not all([zip_path, private_key_path, cert_path, output_path]):
            QMessageBox.warning(self, "警告", "请填写所有必要的文件路径")
            return

        # 创建工作线程
        self.worker = SigningWorker(
            'sign_package',
            zip_path=zip_path,
            private_key_path=private_key_path,
            cert_path=cert_path,
            output_path=output_path
        )
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.message.connect(self.status_label.setText)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()

    def on_operation_finished(self, success, message):
        """操作完成"""
        if success:
            self.result_display.setPlainText(message)
            QMessageBox.information(self, "成功", "操作完成！")
        else:
            QMessageBox.critical(self, "错误", f"操作失败: {message}")

        self.progress_bar.setValue(0)
        self.status_label.setText("就绪")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("CM311-1E Android标准卡刷包签名工具")
    app.setApplicationVersion("1.0")

    # 应用Qt Material主题
    apply_stylesheet(app, theme='dark_teal.xml')

    window = AndroidSignerMainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()

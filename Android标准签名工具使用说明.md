# CM311-1E Android标准卡刷包签名工具使用说明

**作者**: By.举个🌰  
**版权所有**: © 2025  
**版本**: v1.0

## 🎯 工具简介

CM311-1E Android标准卡刷包签名工具是按照Android SignApk标准开发的专业签名工具，完全符合您本地 `update.zip` 的标准格式。

### ✅ 主要特性

- **Android SignApk标准**: 完全按照Android官方SignApk标准
- **智能密钥识别**: 选择签名文件夹后自动识别密钥
- **标准文件格式**: 生成 `testkey.pk8` 和 `testkey.x509.pem`
- **标准签名格式**: `Created-By: 1.0 (Android SignApk)`
- **无重复文件**: 确保只有1个MANIFEST.MF文件
- **Recovery密钥**: 自动生成匹配的验证密钥

### 🔧 解决的问题

1. **完全符合标准**: 与您的 `update.zip` 格式完全一致
2. **智能文件夹识别**: 选择签名文件夹后自动找到密钥文件
3. **标准密钥格式**: 支持 `testkey.pk8` (PKCS#8) 和 `testkey.x509.pem` (X.509)
4. **标准签名头**: `Created-By: 1.0 (Android SignApk)`

## 📋 使用步骤

### 第一步：生成Android标准密钥

1. 启动工具：`python "CM311-1E标准卡刷包签名工具.py"`
2. 在"Android标准密钥生成"区域：
   - 设置密钥大小（推荐2048位）
   - 选择签名文件夹
   - 点击"生成Android标准密钥"
3. 生成的文件：
   - `testkey.pk8` - 私钥文件（PKCS#8格式）
   - `testkey.x509.pem` - 证书文件（X.509格式）
   - `recovery_keys` - Recovery验证密钥

### 第二步：智能密钥识别

1. 在"签名文件夹"中选择包含密钥的文件夹
2. 工具会自动识别以下文件：
   - `testkey.pk8` 和 `testkey.x509.pem` (优先)
   - `platform.pk8` 和 `platform.x509.pem`
   - `releasekey.pk8` 和 `releasekey.x509.pem`
   - `media.pk8` 和 `media.x509.pem`
   - `shared.pk8` 和 `shared.x509.pem`
   - 或任何 `*.pk8` 和 `*.x509.pem` 文件

### 第三步：签名卡刷包

1. 在"Android标准卡刷包签名"区域：
   - 选择要签名的.zip卡刷包
   - 确认自动识别的密钥文件
   - 设置输出文件路径
   - 点击"Android标准签名"
2. 生成的文件：
   - 签名后的卡刷包（如：update_signed.zip）
   - Recovery验证密钥文件（如：update_recovery_keys）

## 🔍 标准格式验证

### 标准MANIFEST.MF格式

```
Manifest-Version: 1.0
Created-By: 1.0 (Android SignApk)

Name: system/build.prop
SHA-256-Digest: JXU7Qs7JPFKnf16EFslgEuDMpqr9hrjiORp8cQbeT70=

Name: boot.img
SHA-256-Digest: GjaunFj3WPfAAIyfXjlUyaO7fVdIw3YCWL8WtP4WH0Q=
```

### 标准签名文件结构

```
META-INF/
├── MANIFEST.MF     (文件清单 - Android SignApk标准)
├── CERT.SF         (签名文件)
└── CERT.RSA        (证书文件)
```

### 验证方法

```python
import zipfile
with zipfile.ZipFile('your_signed_package.zip', 'r') as zf:
    manifest = zf.read('META-INF/MANIFEST.MF').decode('utf-8')
    if 'Created-By: 1.0 (Android SignApk)' in manifest:
        print("✅ 标准Android SignApk格式")
    else:
        print("❌ 非标准格式")
```

## 🗂️ 文件夹结构示例

### 推荐的签名文件夹结构

```
signing_keys/
├── testkey.pk8           # 私钥文件
├── testkey.x509.pem      # 证书文件
└── recovery_keys         # Recovery验证密钥
```

### 支持的密钥文件名

- **标准密钥**: `testkey.pk8` + `testkey.x509.pem`
- **平台密钥**: `platform.pk8` + `platform.x509.pem`
- **发布密钥**: `releasekey.pk8` + `releasekey.x509.pem`
- **媒体密钥**: `media.pk8` + `media.x509.pem`
- **共享密钥**: `shared.pk8` + `shared.x509.pem`

## ⚠️ 注意事项

### 密钥格式要求

- **私钥**: 必须是PKCS#8格式的.pk8文件
- **证书**: 必须是X.509格式的.x509.pem文件
- **编码**: 私钥使用DER编码，证书使用PEM编码

### 刷机要求

- **Recovery版本**: 需要支持Android SignApk标准的Recovery
- **Bootloader**: 设备bootloader必须已解锁
- **设备匹配**: 确保卡刷包与设备型号匹配
- **备份数据**: 刷机前务必备份重要数据

### 文件路径

- **避免空格**: 文件路径不要包含空格和特殊字符
- **路径长度**: Windows系统注意路径长度限制
- **权限**: 确保有足够的读写权限

## 🚨 故障排除

### 自动识别失败

1. **检查文件名**: 确保密钥文件名符合标准
2. **检查格式**: 确保是.pk8和.x509.pem格式
3. **手动选择**: 可以手动浏览选择密钥文件

### 签名失败

1. **密钥格式**: 检查私钥是否为PKCS#8格式
2. **证书格式**: 检查证书是否为X.509格式
3. **文件权限**: 确保有读写权限
4. **磁盘空间**: 确保有足够的磁盘空间

### 刷机失败

1. **格式验证**: 确认MANIFEST.MF包含"Android SignApk"
2. **Recovery兼容**: 确保Recovery支持Android SignApk标准
3. **密钥匹配**: 确保使用正确的Recovery验证密钥

## 💡 最佳实践

### 密钥管理

1. **统一命名**: 使用标准的testkey.pk8和testkey.x509.pem
2. **安全存储**: 将密钥文件存放在安全的文件夹中
3. **备份密钥**: 定期备份密钥文件到安全位置
4. **版本控制**: 为不同版本使用不同的密钥

### 签名流程

1. **测试环境**: 先在测试设备上验证
2. **备份原版**: 保留原始未签名的卡刷包
3. **验证格式**: 签名后验证MANIFEST.MF格式
4. **文档记录**: 记录密钥和签名的对应关系

## 🎉 成功标志

### 签名成功的标志

- ✅ 只有3个META-INF文件
- ✅ 只有1个MANIFEST.MF文件
- ✅ MANIFEST.MF包含"Created-By: 1.0 (Android SignApk)"
- ✅ Recovery密钥格式正确（v4格式）
- ✅ 与标准update.zip格式完全一致

### 刷机成功的标志

- ✅ Recovery能识别签名包
- ✅ Android SignApk验证通过
- ✅ 安装过程正常
- ✅ 设备正常启动

## 📞 技术支持

### 常见问题

**Q: 为什么要选择签名文件夹而不是刷机包？**
A: 这样可以自动识别密钥文件，提高效率，符合Android开发习惯。

**Q: 支持哪些密钥格式？**
A: 支持PKCS#8格式的.pk8私钥和X.509格式的.x509.pem证书。

**Q: 与标准update.zip有什么区别？**
A: 没有区别！完全按照相同的Android SignApk标准生成。

---

**免责声明**: 刷机有风险，操作需谨慎。作者不承担因使用本工具导致的任何设备损坏或数据丢失。
